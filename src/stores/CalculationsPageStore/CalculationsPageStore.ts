import { addDays, format, isAfter, startOfDay } from 'date-fns'
import {
  AllowedZonedMode,
  IBaseCalcInput,
  IGetCalculationXlsInput,
  ISaveDataStationInput,
  StageType,
  TCalcGenerationMethod,
} from 'entities/api/calculationsManager.entities'
import { IStage, IValueForTableModalUpdateValue, UpdateVaultColumnsCode } from 'entities/pages/calcPage.entities.tsx'
import {
  AbortRequestReason,
  CalculationTaskType,
  PlanningStage,
  PlantType,
  TaskStatus,
  TAsyncPartialTask,
} from 'entities/shared/common.entities.ts'
import { ROLES } from 'entities/shared/roles.entities.ts'
import {
  ActualPlanningStage,
  IAcceptErrorResponse,
  ICalculationsPageStore,
  StagesInitStatus,
} from 'entities/store/calculationPageStore.entities.ts'
import { MessagesWarnings } from 'entities/widgets/Vault.entities.ts'
import { makeAutoObservable, runInAction } from 'mobx'
import { ICalcOptimizationParams } from 'shared/api/calculationsManager/calculationsManager'
import api from 'shared/api/index'
import { getDateByUrlOrNextDateAfterCurrentByCurrentRoute } from 'shared/lib/dateFormates'
import { locationParse } from 'shared/lib/locationParse'
import { prepareTelemetryRows } from 'shared/lib/prepareTelemetryTableData'
import { saveFile } from 'shared/lib/saveFile'
import { validateDate } from 'shared/lib/validateDate'
import { ChartProps } from 'shared/ui/Chart/Chart'
import { AvrchmStore } from 'stores/CalculationsPageStore/AvrchmStore.ts'
import { IAllowedZone } from 'stores/CalculationsPageStore/CalculationsPageStore.types.ts'
import { VaultStore } from 'stores/CalculationsPageStore/VaultStore/VaultStore.ts'
import { type RootStore } from 'stores/RootStore'

const ACTUAL_ITEM = {
  value: ActualPlanningStage.ACTUAL,
  label: 'Актуальный',
  color: '#000000',
}

export type IVaultUpdateData = {
  tabId: number
  plantId: number
  plantName: string
} & {
  [key in UpdateVaultColumnsCode]?: IValueForTableModalUpdateValue
}

const initialDataForStation = {
  inputValues: {},
  planingStage: null,
  plantId: null,
  plantType: null,
  rows: [],
  rgus: [],
  allowedZones: [],
  maxConsumptionHour: -1,
  minConsumptionHour: -1,
  plantOptimized: false,
  REGULATED_UNIT: null,
  E_MAX_E_MIN: null,
  accepted: false,
  targetDate: '',
  emaxemin: null,
  ISP_DATE: null,
  columnStages: {},
}

const initialTelemetryChartActiveLegendItems = ['вб', 'нб', 'напор', 'Факт', 'ПБР']

export class CalculationsPageStore {
  rootStore: ICalculationsPageStore['rootStore']
  avrchmStore: ICalculationsPageStore['avrchmStore']
  vaultStore: ICalculationsPageStore['vaultStore']
  stages: ICalculationsPageStore['stages'] = []
  acceptedErrors: ICalculationsPageStore['acceptedErrors'] = []
  // TODO неверная логика. посмотреть обсуждение в NEPTUN-2500
  isLoadingReportDailyOutput: ICalculationsPageStore['isLoadingReportDailyOutput'] = null
  reportDailyOutput: ICalculationsPageStore['reportDailyOutput'] = {
    warnings: [],
    errors: [],
    result: '',
  }
  dataForStation: ICalculationsPageStore['dataForStation'] = initialDataForStation
  dataForStationOriginal: ICalculationsPageStore['dataForStation'] = initialDataForStation
  updateStation: ICalculationsPageStore['updateStation'] = null
  actualStage: ICalculationsPageStore['actualStage'] = null
  currentPlantId: ICalculationsPageStore['currentPlantId'] = null
  mode: ICalculationsPageStore['mode'] = null
  selectedStage: ICalculationsPageStore['selectedStage'] = null
  isLoadingInfoStation: ICalculationsPageStore['isLoadingInfoStation'] = false
  isLoadingSourceData: ICalculationsPageStore['isLoadingSourceData'] = false
  isLoadingTelemetry: ICalculationsPageStore['isLoadingTelemetry'] = false
  loadingTelemetryByDateStatus: ICalculationsPageStore['loadingTelemetryByDateStatus'] = TaskStatus.NOT_EXECUTED
  telemetry: ICalculationsPageStore['telemetry'] = null
  telemetryTableData: ICalculationsPageStore['telemetryTableData'] = []
  telemetryChartActiveLegendItems: ICalculationsPageStore['telemetryChartActiveLegendItems'] =
    initialTelemetryChartActiveLegendItems
  loadStages: ICalculationsPageStore['loadStages'] = false
  calcPossibility: ICalculationsPageStore['calcPossibility'] = null
  telemetryTableDate: ICalculationsPageStore['telemetryTableDate'] = format(new Date(), 'yyyy-MM-dd')
  isStagesExist: ICalculationsPageStore['isStagesExist'] = StagesInitStatus.idle
  date: Date = getDateByUrlOrNextDateAfterCurrentByCurrentRoute('calculations')
  selectLeftMenu: number = 0
  syncTaskStatus: ICalculationsPageStore['syncTaskStatus'] = {
    [MessagesWarnings.SAVE]: TaskStatus.NOT_EXECUTED,
    [MessagesWarnings.UPDATE_ACCEPT]: TaskStatus.NOT_EXECUTED,
    [MessagesWarnings.INITIALIZE]: TaskStatus.NOT_EXECUTED,
    [MessagesWarnings.UPDATE_ACCEPT_CANCEL]: TaskStatus.NOT_EXECUTED,
  }
  calculationXlsExportStatus: ICalculationsPageStore['calculationXlsExportStatus'] = 'DONE'
  plantsListForAside: ICalculationsPageStore['plantsListForAside'] = []
  typeSort: ICalculationsPageStore['typeSort'] = 'custom'
  selectedCellAllowedZones: IAllowedZone[] = []

  constructor(rootStore: RootStore, avrchmStore: AvrchmStore, vaultStore: VaultStore) {
    this.rootStore = rootStore
    this.avrchmStore = avrchmStore
    this.vaultStore = vaultStore
    makeAutoObservable(this)
  }

  get isActualStage() {
    if (this.rootStore.godModeStore.godMode) return true

    return !!this.actualStage
  }

  get viewOnly() {
    return !!this.plantsListForAside.find((el) => el.value === this.selectLeftMenu)?.viewOnly
  }

  get isLastDay() {
    if (this.rootStore.godModeStore.godMode) return false

    return this.date ? !isAfter(startOfDay(this.date), startOfDay(new Date())) : false
  }

  get editMode() {
    return this.rootStore.authStore.userDetail.roles
      .map((el) => el.role)
      .some((el) => [ROLES.TECHNOLOGIST].includes(el))
  }

  get isFinishStage() {
    if (this.rootStore.godModeStore.godMode) return false

    return this.stages.find((el) => el.value === this.selectedStage)?.finished ?? false
  }

  get isSomePlantSelected() {
    return (
      (this.plantsListForAside.filter((el) => el.mixing).length !== 0 && this.selectLeftMenu === 0) ||
      (this.plantsListForAside.length > 0 && this.selectLeftMenu !== 0)
    )
  }

  setSelectedCellAllowedZones = (allowedZones: IAllowedZone[]) => {
    this.selectedCellAllowedZones = allowedZones
  }

  loadPlantsListForAside = async (sortByOrder: boolean) => {
    try {
      const planingStage = this.selectedStage === ACTUAL_ITEM.value ? this.actualStage?.code : this.selectedStage
      const date = format(this.date, 'yyyy-MM-dd')
      const plants = await api.calcModelManager.getPlanPlants({ date, sortByOrder, planingStage, plantType: 'GES' })
      if (plants.length === 0) return
      const leftMenuCalcRaw = sessionStorage.getItem('left-menu-calc')
      const leftMenuCalc: number[] | undefined = leftMenuCalcRaw ? JSON.parse(leftMenuCalcRaw) : undefined
      runInAction(() => {
        this.plantsListForAside = plants.map((el, idx) => ({
          ...el,
          order: idx,
          value: el.plantId,
          label: el.name,
          icon: el.viewOnly ? 'view' : 'settings',
          mixing: leftMenuCalc === undefined ? el.mixing : leftMenuCalc?.some((item) => item === el.plantId),
        }))
      })
      this.vaultStore.initActionsLeft(this.plantsListForAside)
      runInAction(() => {
        if (sortByOrder) {
          this.typeSort = 'custom'
        } else {
          this.typeSort = 'alphabeat'
        }
      })
    } catch (e) {
      console.log(e)
    }
  }

  refreshPlantsListVisuals = async () => {
    try {
      const sortByOrder = this.typeSort === 'custom'
      const planingStage = this.selectedStage === ACTUAL_ITEM.value ? this.actualStage?.code : this.selectedStage
      const date = format(this.date, 'yyyy-MM-dd')

      const plants = await api.calcModelManager.getPlanPlants({
        date,
        sortByOrder,
        planingStage,
        plantType: 'GES',
      })

      const leftMenuCalcRaw = sessionStorage.getItem('left-menu-calc')
      const leftMenuCalc: number[] | undefined = leftMenuCalcRaw ? JSON.parse(leftMenuCalcRaw) : undefined

      runInAction(() => {
        this.plantsListForAside = plants.map((el, idx) => ({
          ...el,
          order: idx,
          value: el.plantId,
          label: el.name,
          icon: el.viewOnly ? 'view' : 'settings',
          mixing: leftMenuCalc === undefined ? el.mixing : leftMenuCalc?.some((item) => item === el.plantId),
        }))
      })
    } catch (e) {
      console.log(e)
      runInAction(() => {
        this.plantsListForAside = []
      })
    }
  }

  setPlantsListForAside: ICalculationsPageStore['setPlantsListForAside'] = (plantsListForAside) => {
    this.plantsListForAside = plantsListForAside
  }

  resetData() {
    this.dataForStation = { ...this.dataForStationOriginal }
  }

  sendWarningsNotifications(warnings: string[], multiError?: boolean) {
    if (warnings?.length > 0) {
      this.rootStore.notificationStore.addNotification({
        title: `Ошибка при сохранении`,
        description: warnings.join(' ;'),
        type: 'error',
        ...(multiError && { multiError }),
      })
    }
  }

  resetStore() {
    this.stages = []
    this.acceptedErrors = []
    this.isLoadingReportDailyOutput = null
    this.reportDailyOutput = {
      warnings: [],
      errors: [],
      result: '',
    }
    this.dataForStation = initialDataForStation
    this.dataForStationOriginal = initialDataForStation
    this.updateStation = null
    this.actualStage = null
    this.currentPlantId = null
    this.mode = null
    this.selectedStage = null
    this.isLoadingInfoStation = false
    this.isLoadingSourceData = false
    this.isLoadingTelemetry = false
    this.telemetry = null
    this.telemetryTableData = []
    this.loadStages = false
    this.calcPossibility = null
    this.loadingTelemetryByDateStatus = TaskStatus.NOT_EXECUTED
    this.telemetryTableDate = format(new Date(), 'yyyy-MM-dd')
    this.isStagesExist = StagesInitStatus.idle
    this.date = addDays(new Date(), 1)
    this.selectLeftMenu = 0
    this.syncTaskStatus = {
      [MessagesWarnings.SAVE]: TaskStatus.NOT_EXECUTED,
      [MessagesWarnings.UPDATE_ACCEPT]: TaskStatus.NOT_EXECUTED,
      [MessagesWarnings.INITIALIZE]: TaskStatus.NOT_EXECUTED,
      [MessagesWarnings.UPDATE_ACCEPT_CANCEL]: TaskStatus.NOT_EXECUTED,
    }
    this.calculationXlsExportStatus = 'DONE'
    this.plantsListForAside = []
    this.typeSort = 'custom'
  }

  getStage(value: string | null) {
    if (value === 'ACTUAL') {
      return this.actualStage?.code
    }

    return value
  }

  changeDate(value: Date) {
    this.date = value
    this.telemetryTableDate = format(new Date(), 'yyyy-MM-dd')
  }

  changeSelectLeftMenu(value: number) {
    this.dataForStation = initialDataForStation
    this.dataForStationOriginal = initialDataForStation
    this.selectLeftMenu = value
    this.setTelemetryTableDate(format(new Date(), 'yyyy-MM-dd'))
  }

  setSelectedStage = (stage: ICalculationsPageStore['selectedStage']) => {
    this.selectedStage = stage
  }

  getColorItem = (type: string) => {
    if (type.toUpperCase().includes('ВСВГО')) {
      return 'var(--color-stage-vsvgo)'
    }

    return 'var(--color-stage-rsv)'
  }

  checkStage = (stages: ICalculationsPageStore['stages'], selectStage: string | null, actualStage: StageType) => {
    const select = selectStage ? selectStage.toUpperCase() : null
    const selectedStageInStage = stages.find((s) => s.value === select)
    if (selectedStageInStage) {
      return selectedStageInStage.value
    } else if (actualStage) {
      return ACTUAL_ITEM.value
    } else {
      const [first] = stages

      return first?.value ?? null
    }
  }

  initStages = async () => {
    const { selectstage = null } = locationParse(location.search)
    const date = format(this.date, 'yyyy-MM-dd')

    this.loadStages = true
    this.isStagesExist = StagesInitStatus.loading
    try {
      const { actualStage, stages } = await api.calculationsManager.getStages({
        calcDate: date,
      })
      runInAction(() => {
        this.actualStage = actualStage
        const stagesTemp = actualStage
          ? [
              ACTUAL_ITEM,
              ...stages.map((item) => {
                const color = this.getColorItem(item.title)

                return {
                  value: item.code,
                  label: item.title,
                  color,
                  finished: item.finished,
                }
              }),
            ]
          : stages.map((item) => {
              const color = this.getColorItem(item.title)

              return { value: item.code, label: item.title, color, finished: item.finished }
            })
        this.stages = stagesTemp as ICalculationsPageStore['stages']
        const tempSelectStage = this.checkStage(
          stagesTemp as ICalculationsPageStore['stages'],
          selectstage,
          actualStage,
        )
        this.setSelectedStage(tempSelectStage)
        if (stages.length) {
          this.isStagesExist = StagesInitStatus.loadedWithData
        } else {
          this.isStagesExist = StagesInitStatus.loadedWithoutData
        }
      })
    } catch (e) {
      console.log(e)
    } finally {
      this.loadStages = false
    }
  }

  changeActualStage = (code: PlanningStage, title: string) => {
    runInAction(() => {
      this.actualStage = { code, title, finished: false }
    })
  }

  checkCalcPossibility = async (
    calcDate: string,
    ps: ICalculationsPageStore['stages'][0]['value'],
    plantId: number,
  ) => {
    try {
      let planingStage: PlanningStage | undefined
      if (ps === ACTUAL_ITEM.value && this.actualStage?.code) {
        planingStage = this.actualStage.code
      } else if (ps !== ACTUAL_ITEM.value) {
        planingStage = ps
      }
      if (planingStage !== undefined) {
        const res = await api.calculationsManager.getCalcPossibility(calcDate, planingStage, plantId)
        runInAction(() => {
          this.calcPossibility = res
        })
      }
    } catch (e) {
      console.log(e)
    }
  }

  setAcceptObject = async (
    plantId: number,
    targetDate: string,
    ps: ICalculationsPageStore['stages'][0]['value'],
    forced: boolean,
  ) => {
    try {
      let planingStage: PlanningStage | undefined
      if (ps === ACTUAL_ITEM.value && this.actualStage?.code) {
        planingStage = this.actualStage.code
      } else if (ps !== ACTUAL_ITEM.value) {
        planingStage = ps
      }
      if (planingStage !== undefined) {
        const objectPost = {
          plantId,
          targetDate,
          planingStage,
        }
        await api.calculationsManager.setAccept(objectPost, forced)
        this.rootStore.notificationStore.addNotification({
          title: 'Данные акцептованы',
          description: '',
          type: 'success',
        })
        if (forced) {
          this.acceptedErrors = []
        }
      }
    } catch (e) {
      if (plantId === this.selectLeftMenu) {
        const error = e as IAcceptErrorResponse
        if (error.status === 400) {
          this.acceptedErrors = error?.details.length > 0 ? error.details : []
        } else {
          console.log(e)
        }
      }
      throw e
    }
  }

  resetAcceptErrors = () => {
    this.acceptedErrors = []
  }

  setDisacceptObject = async (
    plantId: number,
    targetDate: string,
    ps: ICalculationsPageStore['stages'][0]['value'],
  ) => {
    try {
      let planingStage: PlanningStage | undefined
      if (ps === ACTUAL_ITEM.value && this.actualStage?.code) {
        planingStage = this.actualStage.code
      } else if (ps !== ACTUAL_ITEM.value) {
        planingStage = ps
      }
      if (planingStage) {
        const objectPost = {
          plantId,
          targetDate,
          planingStage,
        }
        await api.calculationsManager.setDisaccept(objectPost)
      }
    } catch (e) {
      console.log(e)
      throw e
    }
  }

  calcAllowedZones = async (plantId: number, targetDate: string, ps: ICalculationsPageStore['stages'][0]['value']) => {
    try {
      let planingStage: PlanningStage | undefined
      if (ps === ACTUAL_ITEM.value && this.actualStage?.code) {
        planingStage = this.actualStage.code
      } else if (ps !== ACTUAL_ITEM.value) {
        planingStage = ps
      }
      if (planingStage) {
        this.rootStore.notificationStore.addNotification({
          title: `Расчёт допустимых зон`,
          description: `Расчёт запущен`,
          type: 'success',
        })
        const objectPost = {
          plantId,
          targetDate,
          planingStage,
        }
        this.updateCalcPossibilityTaskStatus({
          status: TaskStatus.IN_PROCESS,
          type: { code: CalculationTaskType.CALC_ALLOWED_ZONES },
        })
        await api.calculationsManager.calcAllowedZones(objectPost)
      }
    } catch (e) {
      this.rootStore.notificationStore.addNotification({
        title: `Расчёт допустимых зон`,
        description: `Произошла ошибка при расчёте допустимых зон`,
        type: 'error',
      })
      this.updateCalcPossibilityTaskStatus({
        status: TaskStatus.FAILED,
        type: { code: CalculationTaskType.CALC_ALLOWED_ZONES },
      })
      console.log(e)
    }
  }

  calcGeneration = async (
    plantId: number,
    targetDate: string,
    ps: ICalculationsPageStore['stages'][0]['value'],
    method?: TCalcGenerationMethod,
  ) => {
    try {
      let planingStage: PlanningStage | undefined
      if (ps === ACTUAL_ITEM.value && this.actualStage?.code) {
        planingStage = this.actualStage.code
      } else if (ps !== ACTUAL_ITEM.value) {
        planingStage = ps
      }
      if (planingStage) {
        if (method === 'MAXIMUM') {
          this.rootStore.notificationStore.addNotification({
            title: `Расчёт максимальной генерации`,
            description: `Расчёт максимальной генерации начался`,
            type: 'success',
          })
        } else {
          this.rootStore.notificationStore.addNotification({
            title: `Расчёт плановой генерации`,
            description: `Расчёт плановой генерации начался`,
            type: 'success',
          })
        }
        const objectPost = {
          plantId,
          targetDate,
          planingStage,
          method,
        }
        this.updateCalcPossibilityTaskStatus({
          status: TaskStatus.IN_PROCESS,
          type: { code: CalculationTaskType.CALC_GENERATION },
        })
        await api.calculationsManager.calcGeneration(objectPost)
      } else {
        this.rootStore.notificationStore.addNotification({
          title: `Расчёт планового графика генерации`,
          description: `Расчёт планового графика генерации выполнен успешно`,
          type: 'success',
        })
      }
    } catch (e) {
      this.updateCalcPossibilityTaskStatus({
        status: TaskStatus.FAILED,
        type: { code: CalculationTaskType.CALC_GENERATION },
      })
      this.rootStore.notificationStore.addNotification({
        title: `Расчёт планового графика генерации`,
        description: `Произошла ошибка при расчёте планового графика генерации`,
        type: 'error',
      })
      console.log(e)
    }
  }

  calcEnteringAllowedZones = async (
    plantId: number,
    targetDate: string,
    ps: ICalculationsPageStore['stages'][0]['value'],
    mode: AllowedZonedMode,
  ) => {
    try {
      this.mode = mode
      if (this.mode === 'CONSUMPTION_SCHEDULE_CHANGE') {
        this.rootStore.notificationStore.addNotification({
          title: `Ввод в допустимые зоны относительно графика потребления`,
          description: `Расчёт запущен`,
          type: 'success',
        })
        this.updateCalcPossibilityTaskStatus({
          status: TaskStatus.IN_PROCESS,
          type: { code: CalculationTaskType.CALC_ENTERING_ALLOWED_ZONES },
        })
      } else {
        this.rootStore.notificationStore.addNotification({
          title: `Ввод в допустимые зоны в сторону ближайшей границы`,
          description: `Расчёт запущен`,
          type: 'success',
        })
        this.updateCalcPossibilityTaskStatus({
          status: TaskStatus.IN_PROCESS,
          type: { code: CalculationTaskType.CALC_ENTERING_ALLOWED_ZONES_TO_BOUNDS },
        })
      }

      const objectPost = {
        plantId,
        targetDate,
        planingStage: (ps === ACTUAL_ITEM.value ? this.actualStage?.code : ps) as PlanningStage,
      }
      await api.calculationsManager.calcEnteringAllowedZones(objectPost, mode)

      return true
    } catch (e) {
      if (this.mode === 'CONSUMPTION_SCHEDULE_CHANGE') {
        this.updateCalcPossibilityTaskStatus({
          status: TaskStatus.FAILED,
          type: { code: CalculationTaskType.CALC_ENTERING_ALLOWED_ZONES },
        })
        this.rootStore.notificationStore.addNotification({
          title: `Ввод в допустимую область относительно графика потребления`,
          description: `Произошла ошибка при вводе в допустимую область относительно графика потребления`,
          type: 'error',
        })
      } else {
        this.updateCalcPossibilityTaskStatus({
          status: TaskStatus.FAILED,
          type: { code: CalculationTaskType.CALC_ENTERING_ALLOWED_ZONES_TO_BOUNDS },
        })
        this.rootStore.notificationStore.addNotification({
          title: `Ввод в допустимую область относительно ближайшей границы`,
          description: `Произошла ошибка при вводе в допустимую область относительно ближайшей границы`,
          type: 'error',
        })
      }
      console.log(e)

      return false
    }
  }

  loadDataForStation = async (
    _: number,
    calcDate: string,
    ps: ICalculationsPageStore['stages'][0]['value'],
    abortController: AbortController,
  ) => {
    try {
      this.isLoadingInfoStation = true
      await this.checkCalcPossibility(calcDate, ps, this.selectLeftMenu)
      const {
        inputValues,
        planingStage,
        plantId,
        rows,
        targetDate,
        allowedZones,
        plantType,
        rgus,
        accepted,
        maxConsumptionHour,
        minConsumptionHour,
        parameters,
        columnStages,
        needInit,
      } = await api.calculationsManager.getDataForStation(
        this.selectLeftMenu,
        calcDate,
        (ps === ACTUAL_ITEM.value ? this.actualStage?.code : ps) as PlanningStage,
        abortController.signal,
      )
      const ISP_DATE = inputValues?.ISP_DATE?.value ? inputValues.ISP_DATE.value : null
      const emaxemin = parameters['E_MAX_E_MIN'] ? parameters['E_MAX_E_MIN']!.value : { turnedOn: false, value: 0 }
      const E_MAX_E_MIN = emaxemin?.value ? emaxemin.value / 1000 : null
      const plantOptimized = parameters['OPTIMIZATION']?.value?.turnedOn ?? false
      const REGULATED_UNIT = parameters['REGULATED_UNIT']?.value ?? null
      const inputValuesRes = inputValues['W_MIN']
        ? {
            ...inputValues,
            W_MIN: {
              ...inputValues['W_MIN'],
              calculate: !!parameters['E_MAX_E_MIN']?.value.turnedOn,
            },
          }
        : inputValues
      const dataForStation = {
        inputValues: inputValuesRes,
        planingStage,
        plantId,
        rows,
        targetDate,
        allowedZones,
        plantType,
        rgus,
        accepted,
        E_MAX_E_MIN,
        maxConsumptionHour,
        minConsumptionHour,
        plantOptimized,
        REGULATED_UNIT,
        emaxemin,
        ISP_DATE,
        columnStages,
        needInit,
      }

      runInAction(() => {
        this.dataForStation = { ...dataForStation }
        this.dataForStationOriginal = { ...dataForStation }
        this.telemetryChartActiveLegendItems = initialTelemetryChartActiveLegendItems
      })

      return { ...dataForStation }
    } catch (e) {
      console.log(e)
      if (abortController.signal.reason !== AbortRequestReason.REPEAT_REQUEST) {
        this.dataForStation = {
          REGULATED_UNIT: null,
          accepted: false,
          plantOptimized: false,
          inputValues: {},
          planingStage: null,
          plantId: null,
          rows: [],
          targetDate: '',
          allowedZones: [],
          rgus: [],
          plantType: PlantType.GES,
          E_MAX_E_MIN: null,
          maxConsumptionHour: -1,
          minConsumptionHour: -1,
          emaxemin: null,
          ISP_DATE: null,
          columnStages: {},
          needInit: undefined,
        }
      }

      return null
    } finally {
      setTimeout(() => {
        this.isLoadingInfoStation = false
      }, 0)
    }
  }

  inizCalc = async (params: IBaseCalcInput) => {
    try {
      await api.calculationsManager.inizCalc(params)
    } catch (e) {
      console.log(e)
      throw e
    }
  }

  calcOptimization = async (params: ICalcOptimizationParams) => {
    try {
      this.updateCalcPossibilityTaskStatus({
        status: TaskStatus.IN_PROCESS,
        type: { code: CalculationTaskType.OPTIMIZATION },
      })
      await api.calculationsManager.calcOptimization(params)
      this.rootStore.notificationStore.addNotification({
        title: `Оптимизация`,
        description: `Расчёт запущен`,
        type: 'success',
      })

      return true
    } catch (error) {
      this.updateCalcPossibilityTaskStatus({
        status: TaskStatus.FAILED,
        type: { code: CalculationTaskType.OPTIMIZATION },
      })
      console.log(error)

      return false
    }
  }

  calcRGE = async (pId: number, calcDate: string, ps: ICalculationsPageStore['stages'][0]['value']) => {
    try {
      this.updateCalcPossibilityTaskStatus({
        status: TaskStatus.IN_PROCESS,
        type: { code: CalculationTaskType.CALC_BALANCE_RGU },
      })
      const objectPost = {
        plantId: pId,
        targetDate: calcDate,
        planingStage: (ps === ACTUAL_ITEM.value ? this?.actualStage?.code : ps) as PlanningStage,
      }
      await api.calculationsManager.rguGeneration(objectPost)
      this.rootStore.notificationStore.addNotification({
        title: `Распределение нагрузки по РГЕ`,
        description: `Расчёт запущен`,
        type: 'success',
      })
    } catch (e) {
      this.updateCalcPossibilityTaskStatus({
        status: TaskStatus.FAILED,
        type: { code: CalculationTaskType.CALC_BALANCE_RGU },
      })
      console.log(e)
    }
  }

  saveDataStation = async (objectPost: ISaveDataStationInput, isTooltip: boolean) => {
    try {
      const { warnings } = await api.calculationsManager.saveDataStation(objectPost)
      this.sendWarningsNotifications(warnings, true)
      if (isTooltip) {
        this.rootStore.notificationStore.addNotification({
          title: 'Сохранение',
          description: 'Данные сохранены',
          type: 'success',
        })
      }

      return true
    } catch (e) {
      console.log(e)

      return false
    }
  }

  startLoadTheSourceData = () => {
    this.isLoadingSourceData = true
  }

  stopLoadTheSourceData = (plantId: number, date: string, stage: IStage, isDone: boolean) => {
    this.isLoadingSourceData = false
    if (isDone) {
      this.updateStation = {
        plantId,
        date,
        stage,
      }
    } else {
      this.updateStation = null
    }
  }

  getIspDate(value: string): Date {
    const [year, month, day] = value.split('-')

    return new Date(`${year}-${month}-${day}`)
  }

  onDownloadTheSourceData = async (
    plantId: number,
    targetDate: string,
    taskType: CalculationTaskType | null,
    ps: string,
    dateISP: Date,
  ) => {
    const updateTaskStatuses = (status: TaskStatus) => {
      const tasksToUpdate: CalculationTaskType[] = taskType
        ? [taskType]
        : [CalculationTaskType.LOAD_CM_DATA, CalculationTaskType.LOAD_CONSUMPTION, CalculationTaskType.LOAD_PLANT_DATA]

      tasksToUpdate.forEach((task) =>
        this.updateCalcPossibilityTaskStatus({
          status,
          type: { code: task },
        }),
      )
    }

    try {
      const planingStage = (ps === ACTUAL_ITEM.value ? this?.actualStage?.code : ps) as PlanningStage

      let ispDate = ``
      if (validateDate(dateISP)) {
        const day = dateISP.getDate()
        const month = dateISP.getMonth() + 1
        const year = dateISP.getFullYear()
        const resMonth = month > 9 ? month : `0${month}`
        const resDay = day > 9 ? day : `0${day}`
        ispDate = `${year}-${resMonth}-${resDay}`
      } else {
        const { year = null, month = null, day = null } = locationParse(location.search)
        const dateISPInit = this.dataForStation.ISP_DATE
          ? this.getIspDate(this.dataForStation.ISP_DATE)
          : year && month && day
            ? new Date(`${year}-${month}-${day}`)
            : new Date()
        const nowDate = new Date(dateISPInit)
        ispDate = `${nowDate.getFullYear()}-${nowDate.getMonth() > 9 ? nowDate.getMonth() : `0${nowDate.getMonth()}`}-${nowDate.getDate() > 9 ? nowDate.getDate() : `0${nowDate.getDate()}`}`
      }

      updateTaskStatuses(TaskStatus.IN_PROCESS)

      await api.calculationsManager.onDownloadTheSourceData(plantId, targetDate, planingStage, taskType, ispDate)

      let description: string = ''
      if (taskType === null) {
        description = `Загрузка исходных данных началась`
      }
      if (taskType === 'LOAD_CONSUMPTION') {
        description = `Загрузка потребления по территориям из ПАК ИСП началась`
      }
      if (taskType === 'LOAD_PLANT_DATA') {
        description = `Загрузка данных из Модес началась`
      }
      if (taskType === 'LOAD_CM_DATA') {
        description = `Загрузка данных из РМ началась`
      }
      if (taskType !== 'LOAD_GENERATOR_ALLOWED_ZONES') {
        this.rootStore.notificationStore.addNotification({
          title: 'Загрузка данных ',
          description,
          type: 'success',
        })
      }
    } catch (e) {
      updateTaskStatuses(TaskStatus.FAILED)
      console.log(e)
    }
  }

  getTelemetry = async (plantId: number, telemetryDate: string, status?: TaskStatus) => {
    try {
      this.isLoadingTelemetry = true
      const res = await api.calculationsManager.getTelemetry({
        plantId,
        telemetryDate,
      })
      runInAction(() => {
        this.telemetry = res
        this.telemetryTableData = prepareTelemetryRows(this.telemetry?.hourItems ?? [])
      })
    } catch (error) {
      console.log(error)
    } finally {
      if (status) {
        this.setLoadingTelemetryByDateStatus(status)
      }
      runInAction(() => {
        this.isLoadingTelemetry = false
      })
    }
  }

  get isLoadingTelemetryInProcess() {
    return this.loadingTelemetryByDateStatus === TaskStatus.IN_PROCESS || this.isLoadingTelemetry
  }

  getTelemetryByDate = async (plantId: number, targetDate: string) => {
    try {
      this.isLoadingTelemetry = true
      const res = await api.calculationsManager.getTelemetryByDate({
        plantId,
        targetDate,
      })
      this.setLoadingTelemetryByDateStatus(res.status)
    } catch (error) {
      console.log(error)
    } finally {
      this.isLoadingTelemetry = false
    }
  }

  setLoadingTelemetryByDateStatus = (status: TaskStatus) => {
    this.loadingTelemetryByDateStatus = status
  }

  setTelemetryTableDate = (date: string | null) => {
    this.telemetryTableDate = date
  }

  uploadDailyOutput = async (file: File) => {
    try {
      this.changeIsLoadingReportDailyOutput(true)
      const formData = new FormData()
      formData.append('xlsFile', file)
      const { warnings, errors, result } = await api.calculationsManager.uploadDailyOutput(formData)
      this.reportDailyOutput = { warnings, errors, result }
      if (errors.length > 0) {
        this.rootStore.notificationStore.addNotification({
          title: 'Загрузка суточной выработки',
          description: result ?? '',
          type: 'error',
        })
      } else {
        this.rootStore.notificationStore.addNotification({
          title: 'Загрузка суточной выработки',
          description: result ?? '',
          type: 'success',
        })
      }

      return { warnings, errors, result }
    } catch (e) {
      console.log(e)

      return null
    } finally {
      this.changeIsLoadingReportDailyOutput(false)
      setTimeout(() => this.changeIsLoadingReportDailyOutput(null))
    }
  }

  changeIsLoadingReportDailyOutput = (value: boolean | null) => {
    this.isLoadingReportDailyOutput = value
  }

  changePlantId = (value: number) => {
    this.currentPlantId = value
  }

  getCalculationXls = async (params: IGetCalculationXlsInput) => {
    try {
      this.calculationXlsExportStatus = 'IN_PROCESS'
      const resp = await api.calculationsManager.getCalculationXls(params)
      saveFile(resp)
      this.calculationXlsExportStatus = 'DONE'
    } catch (error) {
      console.log(error, 'error when try api.calculationsManager.getCalculationXls')
      this.calculationXlsExportStatus = 'FAILED'
      throw error
    }
  }

  private socketToPossibilityTaskMap = {
    [CalculationTaskType.OPTIMIZATION]: MessagesWarnings.DO_OPTIMIZATION,
    [CalculationTaskType.LOAD_CONSUMPTION]: MessagesWarnings.LOAD_ISP,
    [CalculationTaskType.LOAD_PLANT_DATA]: MessagesWarnings.LOAD_MODES,
    [CalculationTaskType.LOAD_CM_DATA]: MessagesWarnings.LOAD_CM,
    [CalculationTaskType.WRITE_PLAN_RGU_DATA]: MessagesWarnings.WRITE_MODES,
    [CalculationTaskType.CALC_ALLOWED_ZONES]: MessagesWarnings.CALCULATE_ALLOWED_ZONES,
    [CalculationTaskType.CALC_GENERATION]: MessagesWarnings.CALCULATE_GENERATION,
    [CalculationTaskType.CALC_ENTERING_ALLOWED_ZONES]: MessagesWarnings.ENTERING_ALLOWED_ZONES,
    [CalculationTaskType.CALC_ENTERING_ALLOWED_ZONES_TO_BOUNDS]: MessagesWarnings.ENTERING_ALLOWED_ZONES_TO_BOUNDS,
    [CalculationTaskType.CALC_BALANCE_RGU]: MessagesWarnings.BALANCE_RGU,
  }

  updateCalcPossibilityTaskStatus = (task: TAsyncPartialTask) => {
    if (!this.calcPossibility) return

    const type = this.socketToPossibilityTaskMap[task.type.code as keyof typeof this.socketToPossibilityTaskMap]

    this.calcPossibility = {
      ...this.calcPossibility,
      taskStatus: {
        ...this.calcPossibility.taskStatus,
        [type]: task.status,
      },
    }
  }

  get telemetrySeries() {
    if (!this.telemetry?.minuteItems?.length) return []

    const series: ChartProps['options']['series'] = []
    const options = {
      вб: {
        color: '#0070c0',
        yAxis: 0,
      },
      нб: {
        color: '#78b151',
        yAxis: 0,
      },
      напор: {
        color: '#a5a5a5',
        yAxis: 0,
      },
      Факт: {
        color: '#ed7d31',
        yAxis: 1,
      },
      ПБР: {
        color: '#5f9dd6',
        yAxis: 1,
      },
    }

    if (this.telemetry) {
      for (const telemetryItem of this.telemetry.minuteItems) {
        const data = telemetryItem.values.map((item) => {
          return [new Date(item.dateTime).getTime(), item.value]
        })
        series.push({
          visible: this.telemetryChartActiveLegendItems.includes(telemetryItem.title),
          type: 'line',
          name: telemetryItem.title,
          data,
          showInNavigator: true,
          ...options[telemetryItem.title],
        })
      }
    }

    return series
  }

  handleClickOnTelemetryChartLegendItems: ICalculationsPageStore['handleClickOnTelemetryChartLegendItems'] = (
    legendItem,
  ) => {
    const idx = this.telemetryChartActiveLegendItems.findIndex((el) => el === legendItem)
    if (idx === -1) {
      this.telemetryChartActiveLegendItems.push(legendItem)
    } else {
      this.telemetryChartActiveLegendItems.splice(idx, 1)
    }
  }

  cleanAvrchmsByHourStation(row: number) {
    this.dataForStation.rgus = this.dataForStation.rgus.map((el) => {
      const allowedZones = el.allowedZones.map((zone, index) => {
        if (index === row) {
          return { ...zone, zones: [] }
        }

        return zone
      })

      return { ...el, allowedZones }
    })
    this.dataForStation.allowedZones = this.dataForStation.allowedZones.map((el, index) => {
      if (index === row) {
        return { ...el, zones: [] }
      }

      return el
    })
  }

  cleanAvrchmsByHourStationAll() {
    // todo: удаление данных о зонах, после включения режима половодья.
    // ПРИЧИНА ОШИБКИ
    this.dataForStation.rgus = this.dataForStation.rgus.map((el) => {
      const allowedZones = el.allowedZones.map((zone) => {
        return { ...zone, zones: [] }
      })

      return { ...el, allowedZones }
    })
    this.dataForStation.allowedZones = this.dataForStation.allowedZones.map((el) => {
      return { ...el, zones: [] }
    })
  }

  setTertiaryReserve(inputValue: string) {
    this.dataForStation.inputValues.TERTIARY_RESERVE.value = inputValue as unknown as number
  }

  setSyncStatus: ICalculationsPageStore['setSyncStatus'] = (key, taskStatus) => {
    this.syncTaskStatus = {
      ...this.syncTaskStatus,
      [key]: taskStatus,
    }
  }
}
