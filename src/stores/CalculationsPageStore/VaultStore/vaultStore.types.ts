import {
  IAcceptVaultOutput,
  IAllowedZone,
  ICalculationSummary,
  ICalculationSummaryItem,
  TStatusUpdateModalVault,
} from 'entities/api/calculationsManager.entities.ts'
import { TStatus } from 'entities/api/nsiManager.entities.ts'
import { ICustomHeader, IVaultNestedHeaders } from 'entities/widgets/Vault.entities.ts'
import Handsontable from 'handsontable'
import { DetailedSettings } from 'handsontable/plugins/collapsibleColumns'
import { IVaultUpdateData } from 'stores/CalculationsPageStore'
import { RootStore } from 'stores/RootStore.ts'
import { SpreadsheetSelectedCells } from 'widgets/Spreadsheet/ui/lib'
import { IInputResultItemProp } from 'widgets/Spreadsheet/ui/Spreadsheet.tsx'

type CellSettings = Exclude<Handsontable.GridSettings['cell'], undefined>[0]
type ColumnSettings = Handsontable.ColumnSettings
type GridSettings = Handsontable.GridSettings

interface IVaultSpreadsheetCell extends CellSettings {
  renderer: string
  isMaxConsumptionHour: boolean
  isMinConsumptionHour: boolean
  editor: GridSettings['editor']
  type: string
  idStation: number
  idRGU: number | null
  keyStation: string
  manual?: boolean
  fixed?: boolean
  allowedZones: IAllowedZone[]
}

interface IVaultSpreadsheet {
  colNumberPerStation: number[]
  nestedHeaders: IVaultNestedHeaders
  customHeaders: ICustomHeader[]
  column: ColumnSettings[]
  collapsibleColumns: DetailedSettings[]
  data: (string | number | undefined | null)[][]
  cell: IVaultSpreadsheetCell[]
  inputResultProps: IInputResultItemProp[]
}

interface IVaultDataItem {
  tabId: string
  hour: string
  [key: string]: number | string | IAllowedZone[]
}

interface ILoadDataForVault {
  vaultData: IVaultDataItem[] | null
  plantsData: ICalculationSummaryItem[]
}

export type InputValueKeys = 'W_MIN' | 'W_MAX' | 'P_GEN_TARGET' | 'FLOOD_MODE_WATCH'

type IVaultInputValue = {
  plantId: number
} & { [key in InputValueKeys]?: number | string | boolean }

export interface IAvrchmSpreadsheetColumn {
  columnIdx: number
  plantId: number
}

export interface IVaultStore {
  rootStore: RootStore
  vaultLoadDataStatus: TStatus
  vaultSpreadsheet: IVaultSpreadsheet
  originalVaultSpreadsheet: IVaultSpreadsheet
  vaultData: IVaultDataItem[]
  vaultDataOriginal: IVaultDataItem[]
  plantsData: ICalculationSummary['plants']
  plantsDataOriginal: ICalculationSummary['plants']
  statusVaultUpdateData: TStatusUpdateModalVault
  _selectedCellIdx: number | null
  actionsLeft: number[]
  floodsLeft: number[]
  floodsLeftOriginal: number[]
  idsEditFloodsLeft: number[]
  isLoadingAvrchmTes: boolean
  isLoadingVaultUpdateData: boolean
  isModalUpdateVault: boolean
  vaultUpdateData: IVaultUpdateData[]
  isViewModalAcceptedVault: boolean
  plantsAcceptVault: IAcceptVaultOutput[]
  plantsAcceptVaultSelected: number[]
  inputValues: IVaultInputValue[]
  inputValuesChanged: boolean
  _originalInputValues: IVaultInputValue[]
  editCellsUp: string[]
  selectedCellsBeforeFix: SpreadsheetSelectedCells[]
  shouldUpdateVault: boolean

  setVaultLoadDataStatus: (status: TStatus) => void
  convertVaultDataToSpreadsheetProps: (props: ILoadDataForVault) => void
  setSpreadsheetDataAndCellProp: (
    cellProp: IVaultSpreadsheet['cell'],
    dataProp: IVaultSpreadsheet['data'] | null,
  ) => void
  setSpreadsheetDataProp: (dataProp: IVaultSpreadsheet['data']) => void
  setSpreadsheetCustomHeaderProp: (customHeadersProp: IVaultSpreadsheet['customHeaders']) => void
  setSpreadsheetCellProp: (cellProp: IVaultSpreadsheet['cell']) => void
  setSpreadsheetColumnProp: (columnProp: IVaultSpreadsheet['column']) => void
  setSelectedCell: Handsontable.GridSettings['afterSelection']
  setDeselectedCell: Handsontable.GridSettings['afterDeselect']
  debouncedInitLoadDataVault: () => void
  initLoadDataVault: (shouldSaveRequestStatus?: boolean) => Promise<void>
  setShouldUpdateVault: (shouldUpdateVault: boolean) => void
}
