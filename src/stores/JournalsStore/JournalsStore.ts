import { format } from 'date-fns'
import { prepareDateJournal } from 'entities/store/journalsStore.entities.ts'
import { makeAutoObservable } from 'mobx'
import { compareTimestamp } from 'pages/JournalsPage/utils/utils'
import api from 'shared/api/index'
import {
  IInteractionExternalSystemJournalParams,
  IInteractionExternalSystemJournalResponse,
  IUserActionsCategory,
  IUserActionsJournalParams,
  IUserActionsJournalResponse,
} from 'shared/api/journalsManager/journalsManager'
import { generateUUID } from 'shared/lib/GenerationUUID'
import { type RootStore } from 'stores/RootStore'

export interface IInteractionExternalSystemList extends IInteractionExternalSystemJournalResponse {
  category: string
  tabId: number
  createdDate: string
  createdDateFormatted: string
  status: { title: string }
}

export class JournalsStore {
  rootStore: RootStore
  userActionsLoading: boolean = false
  userActionsJournalList: IUserActionsJournalResponse[] = []
  interactionExternalSystemLoading: boolean = false
  interactionExternalSystemList: IInteractionExternalSystemList[] = []
  userActionsCategoryList: IUserActionsCategory[] = []

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore
    makeAutoObservable(this)
  }

  fetchUserActionsJournal = async (params: IUserActionsJournalParams) => {
    this.userActionsLoading = true
    const paramsTemp = JSON.parse(JSON.stringify(params) as string)
    const fromDate = prepareDateJournal(paramsTemp.fromDate, 'from')
    const toDate = prepareDateJournal(paramsTemp.toDate, 'to')
    const res = {
      ...paramsTemp,
      fromDate,
      toDate,
    }
    try {
      const response = await api.journalsManger.getUserActionsJournal(res)
      this.userActionsJournalList = response.sort(compareTimestamp).map((actions, index) => {
        return {
          ...actions,
          createdDateFormatted: format(new Date(actions.createdDate).getTime(), 'dd.MM.yyy HH:mm:ss'),
          category: actions.category.name,
          tabId: actions.createdDate + index,
        }
      }) as unknown as IUserActionsJournalResponse[]
    } catch (e) {
      console.log(e)
    } finally {
      this.userActionsLoading = false
    }
  }

  fetchInteractionExternalSystemJournal = async (params: IInteractionExternalSystemJournalParams) => {
    this.interactionExternalSystemLoading = true
    const paramsTemp = JSON.parse(JSON.stringify(params) as string)
    const fromDate = prepareDateJournal(paramsTemp.fromDate, 'from')
    const toDate = prepareDateJournal(paramsTemp.toDate, 'to')
    const res = {
      ...paramsTemp,
      fromDate,
      toDate,
    }
    try {
      const response = await api.journalsManger.getInteractionExternalSystem(res)
      this.interactionExternalSystemList = response
        ? (response.sort(compareTimestamp).map((actions) => {
            return {
              ...actions,
              createdDateFormatted: format(new Date(actions.createdDate).getTime(), 'dd.MM.yyy HH:mm:ss'),
              tabId: generateUUID(),
              status: actions?.status?.title,
              interactionKind: actions?.interactionKind?.title,
            }
          }) as unknown as IInteractionExternalSystemList[])
        : []
    } catch (e) {
      console.log(e)
    } finally {
      this.interactionExternalSystemLoading = false
    }
  }

  fetchExternalSystemStatuses = async () => {
    try {
      this.userActionsCategoryList = await api.journalsManger.getUserActionsCategory()
    } catch (e) {
      console.log(e)
    }
  }
}
