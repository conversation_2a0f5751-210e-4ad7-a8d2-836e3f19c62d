import { format } from 'date-fns'
import {
  IGetRegistryOutput,
  IPlanDepartment,
  IPlantsSK11,
  ISaveRegistry,
  TGetRegistryInput,
  TPlantsDiffType,
  TStatus,
} from 'entities/api/nsiManager.entities.ts'
import { TIME_LOADER } from 'entities/constants.ts'
import { THierarchyType } from 'entities/pages/nsiPage.entities.ts'
import { makeAutoObservable, runInAction } from 'mobx'
import api from 'shared/api/index'
import {
  type IRegistryEventsParams,
  type IRegistryEventsResponse,
  type IRegistryProtocolParams,
  type IRegistryProtocolResponse,
} from 'shared/api/nsiManager/nsiManager'
import { generateUUID } from 'shared/lib/GenerationUUID'
import { prepareDate } from 'shared/lib/prepareData'
import { prepareDataAutoComplete, prepareDataAutoCompleteWer } from 'shared/lib/prepareDataAutoComplete'
import { type RootStore } from 'stores/RootStore'

export interface IRegistryEvents extends IRegistryEventsResponse {
  tabId: string
}

export interface ITempRegistry extends IRegistryProtocolResponse {
  uuid: string
  updatedDate: string
}

export interface IRow extends Omit<IGetRegistryOutput, 'children'> {
  value?: number
  label?: string
  id: number
  name: string
  parentId?: number
  uid: string
  type: string
  children: IRow[]
  planDepartments: IPlanDepartment[]
  startDate: string
  endDate: string
  marketCalcModelId: number
  active: boolean
  tabId?: string
  departmentLevel?: string
  werDepartments?: number[] & { name: string; id: number }[]
}

const prepareFlatData = (rows: IRow[]) => {
  let res: IRow[] = []
  rows.forEach((row) => {
    const children = row?.children?.length > 0 ? prepareFlatData(row.children) : []
    res.push({
      ...row,
      value: row.id,
      label: row.name,
      id: row.id,
      name: row.name,
      parentId: row.parentId,
      uid: row.uid,
      type: row.type,
      departmentLevel: row?.departmentLevel ?? '',
      werDepartments: row?.werDepartments ?? [],
    })
    res = [...res, ...children]
  })

  return res
}

export type IRegistry = IRow

export interface IPlantsDiff {
  tabId: string
  updatedFields: [{ parameter: string; oldValue: string; newValue: string }]
  endDate: string
  type: TPlantsDiffType
  priority: number
  uid: string
  werDepartments: {
    name: string
    id: number
    label: string
    value: number
  }
}
export class NsiStore {
  rootStore: RootStore
  registry: IRegistry[] = []
  plantsDiff: IPlantsDiff[] = []
  activeDepartments: IRow[] = []
  infoUploadStations: {
    modelVersion?: number
    saveError?: string
    error?: string
    loadDate?: string
  } = {}
  isUpdateNsi: boolean = false
  isLoadingNsi: boolean = true
  statusNSI: null | TStatus = null
  registryProtocol: IRegistryProtocolResponse[] = []
  registryEvents: IRegistryEvents[] = []
  loadingStationSK11: boolean = false
  hierarchyType: THierarchyType = 'BY_RGU'
  statusTask: {
    status?: TStatus
    updatedDate?: string
    id?: number
  } = {}

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore
    makeAutoObservable(this)
  }

  reset() {
    this.isLoadingNsi = true
  }

  changeHierarchyType(value: THierarchyType) {
    this.hierarchyType = value
  }

  getDisabledEditColumnsRegistry(type: string, hierarchyType: 'BY_GENERATOR' | 'BY_RGU', isGaes: boolean): string[] {
    if (type === 'PLANT') {
      if (isGaes) {
        return ['werDepartments']
      }

      return []
    }
    if (type === 'RGU' || type === 'RGU_RELATION') {
      if (hierarchyType === 'BY_GENERATOR') {
        return ['planDepartments', 'werDepartments']
      } else {
        return ['planDepartments', 'startDate', 'endDate', 'werDepartments']
      }
    }
    if (type === 'GENERATOR') {
      return ['planDepartments', 'werDepartments']
    }
    if (type === 'DEPARTMENT') {
      return ['planDepartments', 'marketCalcModelId', 'startDate', 'endDate', 'werDepartments']
    }

    return ['werDepartments']
  }

  prepareRegistry(arr: IRow[], hierarchyType: 'BY_GENERATOR' | 'BY_RGU', parentUUID = '', parentName = '') {
    return arr.map((el) => {
      const fullPath = parentName + '/' + el.name
      const children: IRow[] = el?.children
        ? (this.prepareRegistry(el.children, hierarchyType, el.uid, fullPath) as unknown as IRow[])
        : []
      const planDepartments = el?.planDepartments?.length > 0 ? prepareDataAutoComplete(el?.planDepartments) : []
      const disabledEditColumns = this.getDisabledEditColumnsRegistry(el.type, hierarchyType, el.plantType === 'GAES')
      const tabId = parentUUID.length > 0 ? parentUUID + '/' + el.uid : el.uid
      const werDepartments = el.werDepartments ? prepareDataAutoCompleteWer(el.werDepartments) : []

      return {
        ...el,
        tabId,
        children,
        rowColor: el.active ? 'white' : 'grayAndBold',
        startDate: el?.startDate ? prepareDate(el?.startDate) : '',
        endDate: el?.endDate ? prepareDate(el?.endDate) : '',
        planDepartments,
        disabledEditColumns,
        fullPath,
        name: el.name,
        werDepartments,
      }
    })
  }

  getLastTaskInfoListOfObjects = async (
    taskType: 'SYNC_REGISTRY' | 'COMPARE_PLANTS' | 'APPLY_PLANTS' | 'EDIT_REGISTRY',
  ) => {
    try {
      if (taskType === 'SYNC_REGISTRY') {
        this.statusNSI = null
        const res = await api.nsiManager.getLastTaskSK11(taskType)
        this.statusNSI = res.status ?? null
        this.statusTask = {
          status: res.status,
          updatedDate: res.finishedDate,
          id: res.activeModelVersion,
        }
      } else {
        this.statusNSI = null
        const res = await api.nsiManager.getLastTaskSK11(taskType)
        this.statusNSI = res.status ?? null
        this.statusTask = res
      }
    } catch (e) {
      console.log(e)
    }
  }

  getRegistry = async (hierarchyType: 'BY_GENERATOR' | 'BY_RGU') => {
    try {
      const registry = await api.nsiManager.getRegistry(hierarchyType)
      const res = registry?.length > 0 ? registry : []
      this.registry = this.prepareRegistry(res as unknown as IRow[], hierarchyType) as unknown as IRegistry[]
    } catch (e) {
      console.log(e)
    }
  }

  initListOfObjects = async (isEditMode: boolean, hierarchyType: 'BY_GENERATOR' | 'BY_RGU') => {
    try {
      this.isLoadingNsi = true
      const activeDepartments = await api.nsiManager.getActiveDepartments()
      this.activeDepartments = prepareFlatData(activeDepartments as unknown as IRow[])
      await this.getRegistry(hierarchyType)
      if (isEditMode) {
        await this.getLastTaskInfoListOfObjects('SYNC_REGISTRY')
      }
    } catch (e) {
      console.log(e)
    } finally {
      setTimeout(() => {
        this.isLoadingNsi = false
      }, TIME_LOADER)
    }
  }

  saveRegistry = async (elements: ISaveRegistry[], hierarchyType: TGetRegistryInput) => {
    try {
      const res = await api.nsiManager.saveRegistry(elements, hierarchyType)
      this.registry =
        res.length > 0 ? (this.prepareRegistry(res as unknown as IRow[], hierarchyType) as unknown as IRegistry[]) : []
      this.rootStore.notificationStore.addNotification({
        title: 'Сохранение НСИ ',
        description: 'НСИ сохранено',
        type: 'success',
      })

      return true
    } catch (e) {
      console.log(e)

      return false
    }
  }

  saveStations = async (plants: IPlantsSK11[], abortSignal: AbortSignal) => {
    try {
      this.loadingStationSK11 = true
      const modelVersion = this.infoUploadStations?.modelVersion ?? 0
      const objectPost = {
        modelVersion,
        plants,
      }

      await api.nsiManager.saveStationSK11(objectPost, abortSignal)
      this.rootStore.notificationStore.addNotification({
        title: 'Обновление Станций ',
        description: 'Станции сохранены',
        type: 'success',
      })

      return true
    } catch (e) {
      if (e instanceof Error) {
        this.infoUploadStations = {
          saveError: e.message,
        }
      } else {
        console.log('Произошла ошибка')
      }

      return false
    } finally {
      setTimeout(() => {
        this.loadingStationSK11 = false
      }, TIME_LOADER)
    }
  }

  getPriorityStations = (type: 'ADDED' | 'EARLY_ADDED' | 'CHANGED' | 'UNCHANGED' | 'DELETED') => {
    if (type === 'ADDED') {
      return 3
    }
    if (type === 'EARLY_ADDED') {
      return 5
    }
    if (type === 'CHANGED') {
      return 2
    }
    if (type === 'UNCHANGED') {
      return 4
    }
    if (type === 'DELETED') {
      return 1
    }

    return 0
  }

  getDisabledEditColumnsStations = (
    type: 'DELETED' | 'CHANGED' | 'ADDED' | 'UNCHANGED' | 'EARLY_ADDED',
    isGaes: boolean,
  ): string[] => {
    const result: string[] = []
    if (type === 'DELETED') {
      result.push(
        'werDepartments',
        'type',
        'department',
        'name',
        'planDepartments',
        'marketCalcModelId',
        'startDate',
        'uid',
      )
    } else if (type === 'CHANGED') {
      result.push(
        'werDepartments',
        'type',
        'department',
        'name',
        'planDepartments',
        'marketCalcModelId',
        'startDate',
        'endDate',
        'uid',
      )
    } else if (type === 'ADDED') {
      result.push('type', 'department', 'name', 'endDate', 'uid')
    } else if (type === 'UNCHANGED') {
      result.push(
        'werDepartments',
        'type',
        'department',
        'name',
        'planDepartments',
        'marketCalcModelId',
        'startDate',
        'endDate',
        'uid',
      )
    } else if (type === 'EARLY_ADDED') {
      result.push('type', 'department', 'name', 'endDate', 'uid')
    }
    if (isGaes && !result.includes('werDepartments')) {
      result.push('werDepartments')
    }

    return result
  }

  initUploadStations = async () => {
    try {
      this.loadingStationSK11 = true
      const { modelVersion, plantsDiff, loadDate } = await api.nsiManager.getStationSK11()
      this.infoUploadStations = { modelVersion, loadDate }
      this.plantsDiff = plantsDiff
        .map((el) => {
          const { type, endDate, startDate } = el
          const disabledEditColumns = this.getDisabledEditColumnsStations(type, el.plantType === 'GAES')
          const werDepartments = el.werDepartments ? prepareDataAutoComplete(el.werDepartments) : []

          return {
            ...el,
            tabId: generateUUID(),
            priority: this.getPriorityStations(type),
            startDate: startDate ? prepareDate(startDate) : '',
            endDate: endDate ? prepareDate(endDate) : '',
            disabledEditColumns,
            werDepartments,
          }
        })
        .sort((a, b) => a.priority - b.priority) as unknown as IPlantsDiff[]
    } catch (e) {
      if (e instanceof Error) {
        this.infoUploadStations = {
          error: e.message,
          saveError: e.message,
        }
      } else {
        console.log('Произошла ошибка')
      }
    } finally {
      setTimeout(() => {
        this.loadingStationSK11 = false
      }, TIME_LOADER)
    }
  }

  resetUploadStation = () => {
    this.infoUploadStations = {}
    this.plantsDiff = []
  }

  startSk11 = async () => {
    try {
      this.isUpdateNsi = true
      const { status } = await api.nsiManager.getLastTaskSK11('SYNC_REGISTRY')
      if (String(status) === 'IN_PROCESS') {
        this.rootStore.notificationStore.addNotification({
          title: 'Внимание',
          description: 'Задача начата другим пользователем. Пожалуйста дождитесь завершения',
          type: 'error',
        })
        await this.getLastTaskInfoListOfObjects('SYNC_REGISTRY')
      } else {
        const res = await api.nsiManager.startSK11()
        this.statusNSI = res.status
        this.statusTask = res
        this.rootStore.notificationStore.addNotification({
          title: 'Обновление НСИ ',
          description: 'Обновление запущено',
          type: 'success',
        })
        await this.getLastTaskInfoListOfObjects('SYNC_REGISTRY')
      }
    } catch (e) {
      console.log(e)
    } finally {
      this.isUpdateNsi = false
    }
  }

  startStatusNsi = (nsi: null | TStatus) => {
    this.statusNSI = nsi ?? null
  }

  resetStatusNsi = () => {
    this.statusNSI = null
  }

  getRegistryProtocol = async (params: IRegistryProtocolParams) => {
    const newParams = {
      ...params,
      fromDate: new Date(
        params.fromDate?.getFullYear(),
        params.fromDate?.getMonth(),
        params.fromDate?.getDate(),
        0,
        0,
        0,
      ),
      toDate: new Date(params.toDate?.getFullYear(), params.toDate?.getMonth(), params.toDate?.getDate(), 23, 59, 59),
    }
    try {
      const registryProtocol = await api.nsiManager.getRegistryProtocol(newParams)
      const tempRegistry: ITempRegistry[] = []

      for (const registry of registryProtocol) {
        tempRegistry.push({ uuid: generateUUID(), ...registry })
      }
      runInAction(() => {
        this.registryProtocol = tempRegistry.map((registry) => ({
          ...registry,
          updatedDate: format(new Date(registry.updatedDate), 'dd.MM.yyy HH:mm:ss'),
        }))
      })
    } catch (e) {
      console.log(e)
    }
  }

  getRegistryEvents = async (params: IRegistryEventsParams) => {
    const newParams = {
      ...params,
      fromDate: params.fromDate
        ? new Date(params.fromDate?.getFullYear(), params.fromDate?.getMonth(), params.fromDate?.getDate(), 0, 0, 0)
        : null,
      toDate: params.toDate
        ? new Date(params.toDate?.getFullYear(), params.toDate?.getMonth(), params.toDate?.getDate(), 23, 59, 59)
        : null,
    }
    try {
      const registryEvents = await api.nsiManager.getRegistryEvents(newParams)
      const tempRegistry: IRegistryEvents[] = []

      for (const registry of registryEvents) {
        tempRegistry.push({ tabId: generateUUID(), ...registry })
      }
      runInAction(() => {
        this.registryEvents = tempRegistry.map((event) => ({
          ...event,
          eventDate: event.eventDate,
        }))
      })
    } catch (e) {
      console.log(e)
    }
  }
}
