import { Check<PERSON>, FormControlLabel, Icon<PERSON>utton, <PERSON><PERSON><PERSON> } from '@mui/material'
import { PickerChangeHandlerContext } from '@mui/x-date-pickers/internals/hooks/usePicker/usePickerValue.types'
import { DateRange, DateRangeValidationError } from '@mui/x-date-pickers-pro'
import { TIME_LOADER } from 'entities/constants.ts'
import { IComponentsProps } from 'entities/pages/journalsPage.entities.ts'
import { ITableFilters } from 'entities/widgets/Table.entities.ts'
import { observer } from 'mobx-react'
import React, { useEffect, useState } from 'react'
import {
  getInteractionExternalSystemJournalXls,
  IInteractionExternalSystemJournalResponse,
  IUserActionsJournalParams,
} from 'shared/api/journalsManager/journalsManager'
import { classNames } from 'shared/lib/classNames/classNames'
import { saveFile } from 'shared/lib/saveFile'
import { sortISODates } from 'shared/lib/utils/dateSorting'
import { DateRangePicker } from 'shared/ui/DateRangePicker'
import { Icon } from 'shared/ui/Icon'
import { SubtitleWithActions } from 'shared/ui/SubtitleWithActions'
import { useStore } from 'stores/useStore'
import { CustomTableCell, Table } from 'widgets/Table'

import cls from './InteractionExternalSystem.module.scss'

export const InteractionExternalSystem = observer((props: IComponentsProps) => {
  const { height } = props
  const { journalsStore } = useStore()
  const { fetchInteractionExternalSystemJournal, interactionExternalSystemList } = journalsStore

  const [params, setParams] = useState<IUserActionsJournalParams>(() => ({
    fromDate: new Date(),
    toDate: new Date(),
  }))
  const [tableFilters, setTableFilters] = useState<ITableFilters[]>([])

  const handleChangeDate = (value: DateRange<Date>, context: PickerChangeHandlerContext<DateRangeValidationError>) => {
    if (context.validationError.some((item) => item)) return

    const [from, to] = value
    setParams((prev) => ({
      ...prev,
      fromDate: from,
      toDate: to,
    }))
  }

  const [loading, setLoading] = useState(true)

  useEffect(() => {
    setLoading(true)
    fetchInteractionExternalSystemJournal(params)
  }, [params])

  const [filteredInteractionExternalSystem, setFilteredInteractionExternalSystem] = useState<
    IInteractionExternalSystemJournalResponse[]
  >([])

  useEffect(() => {
    setFilteredInteractionExternalSystem(interactionExternalSystemList)
    const timeoutId = setTimeout(() => {
      setLoading(false)
      clearTimeout(timeoutId)
    }, TIME_LOADER * 2)
  }, [interactionExternalSystemList])

  const INITIAL_FILTER = {
    status: [
      {
        name: 'SUCCESS',
        label: <div className={cls.RowFilter}>Успешно</div>,
        checked: false,
      },
      {
        name: 'ERROR',
        label: <div className={cls.RowFilter}>Ошибка</div>,
        checked: false,
      },
    ],
    interactionKind: [
      {
        name: 'SENDING',
        label: <div className={cls.RowFilter}>Отправление данных</div>,
        checked: false,
      },
      {
        name: 'RECEIVING',
        label: <div className={cls.RowFilter}>Получение данных</div>,
        checked: false,
      },
    ],
  }

  const [filter, setFilter] = useState(INITIAL_FILTER)

  const handleChangeFilter = (key: 'status' | 'interactionKind', values: string[]) => {
    const newFilters = [...filter[key]].map((item) => ({
      ...item,
      checked: values.includes(item.name),
    }))
    setFilter((prev) => ({ ...prev, [key]: newFilters }))
    const newItems = { ...params }
    delete newItems[key]
    setParams((prev) => (values.length ? { ...prev, [key]: values.join(',') } : newItems))
  }

  const handleChangeFilters = (type: 'status' | 'interactionKind', event: React.ChangeEvent<HTMLInputElement>) => {
    const newFilters = filter[type]
    const index = newFilters.findIndex((item) => item.name === event.target.name)
    newFilters.splice(index, 1, {
      ...newFilters[index],
      checked: event.target.checked,
    })
    handleChangeFilter(
      type,
      newFilters.filter((item) => item.checked).map((item) => item.name),
    )
    setFilter((prev) => ({ ...prev, [type]: newFilters }))
  }

  const columns = [
    {
      name: 'createdDate',
      title: 'Время(МСК)',
      width: 150,
      customSorting: sortISODates,
      render: (value: string, row: any) => row.createdDateFormatted || value,
    },
    { name: 'externalSystem', title: 'Система', width: 190 },
    { name: 'interactionKind', title: 'Вид сообщения', width: 190 },
    { name: 'initiator', title: 'Инициатор', width: 200 },
    { name: 'departmentName', title: 'ДЦ', width: 100 },
    {
      name: 'status',
      title: 'Статус',
      width: 120,
      render: (status: 'Успешно' | 'Ошибка') => {
        return (
          <CustomTableCell className={cls.Td}>
            <div
              style={{
                backgroundColor: status === 'Успешно' ? '#339944' : '#D01414',
              }}
            >
              {status}
            </div>
          </CustomTableCell>
        )
      },
    },
    {
      name: 'description',
      title: 'Сообщение',
      width: 400,
      isBlockedSorting: true,
      render: (description: string) => (
        <div
          style={{
            overflow: 'hidden',
            textWrap: 'nowrap',
            textOverflow: 'ellipsis',
          }}
          title={description}
        >
          {description}
        </div>
      ),
    },
  ]

  const handleDownloadXml = async () => {
    const tableParams = Object.assign(
      {},
      ...tableFilters.map(({ columnName, value }) => ({
        [columnName]: value,
      })),
    )
    await getInteractionExternalSystemJournalXls({
      ...params,
      ...tableParams,
    })
      .then((resp) => {
        saveFile(resp)
      })
      .catch((err) => console.log(err, 'err'))
  }

  const handleFilters = (frontFilters: ITableFilters[]) => {
    setTableFilters(frontFilters)
  }

  return (
    <div>
      <SubtitleWithActions
        title='Журнал взаимодействия с внешними системами'
        actions={[
          <div className={cls.DateContainer} key={1}>
            <DateRangePicker dateFrom={params.fromDate} dateTo={params.toDate} handleChangeDate={handleChangeDate} />
          </div>,
          <Tooltip key={2} title='Скачать'>
            <IconButton style={{ padding: 0 }} onClick={handleDownloadXml}>
              <Icon className={cls.ExcelIcon} name='excel' width={24} height={24} />
            </IconButton>
          </Tooltip>,
        ]}
        isActionsVisible
      />
      <div className={classNames(cls.Body, {}, [])}>
        <Table
          isJournals
          loading={loading}
          getFilters={handleFilters}
          columns={columns}
          rows={filteredInteractionExternalSystem}
          height={height}
          columnSearchDisabled={['status', 'interactionKind']}
          headerComponents={
            <div className={cls.TopFilters}>
              <div className={cls.TopFilterLabels}>
                <h4 className={cls.TopFilterTitle}>Статус</h4>
                {filter.status.map((item) => (
                  <FormControlLabel
                    key={item.name}
                    className={cls.TopFilterLabel}
                    label={item.label}
                    control={
                      <Checkbox
                        className={cls.TopFiltersCheckbox}
                        checked={item.checked}
                        name={item.name}
                        onChange={(value) => handleChangeFilters('status', value)}
                      />
                    }
                  />
                ))}
              </div>
              <div className={cls.TopFilterLabels}>
                <h4 className={cls.TopFilterTitle}>Вид сообщения</h4>
                {filter.interactionKind.map((item) => (
                  <FormControlLabel
                    key={item.name}
                    className={cls.TopFilterLabel}
                    label={item.label}
                    control={
                      <Checkbox
                        className={cls.TopFiltersCheckbox}
                        checked={item.checked}
                        name={item.name}
                        onChange={(value) => handleChangeFilters('interactionKind', value)}
                      />
                    }
                  />
                ))}
              </div>
            </div>
          }
        />
      </div>
    </div>
  )
})
