import { Checkbox, FormControlLabel, IconButton, <PERSON>lt<PERSON> } from '@mui/material'
import { format } from 'date-fns'
import { TIME_LOADER } from 'entities/constants.ts'
import { IComponentsProps } from 'entities/pages/journalsPage.entities.ts'
import { ITableFilters } from 'entities/widgets/Table.entities.ts'
import { observer } from 'mobx-react'
import { useEffect, useState } from 'react'
import {
  getUserActionsJournalXls,
  IUserActionsCategory,
  IUserActionsJournalResponse,
} from 'shared/api/journalsManager/journalsManager'
import { classNames } from 'shared/lib/classNames/classNames'
import { saveFile } from 'shared/lib/saveFile'
import { DateRangePicker } from 'shared/ui/DateRangePicker'
import { DataPickerValue, DatePickerContext } from 'shared/ui/DateRangePicker/ui/DateRangePicker'
import { Icon } from 'shared/ui/Icon'
import { SubtitleWithActions } from 'shared/ui/SubtitleWithActions'
import { useStore } from 'stores/useStore'
import { Table } from 'widgets/Table'

import cls from './UserActionsJournal.module.scss'

export interface ICategories {
  name: string
  label: string
  checked: boolean
}

export const UserActionsJournal = observer((props: IComponentsProps) => {
  const { height } = props
  const { journalsStore } = useStore()
  const { fetchUserActionsJournal, userActionsJournalList, fetchExternalSystemStatuses, userActionsCategoryList } =
    journalsStore

  const [params, setParams] = useState<{
    fromDate: Date | null
    toDate: Date | null
  }>(() => ({
    fromDate: new Date(),
    toDate: new Date(),
  }))
  const [filtersFromFront, setFiltersFromFront] = useState<ITableFilters[]>([])
  const [filter, setFilter] = useState<{ categories: ICategories[] }>({
    categories: [],
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    setLoading(true)
    fetchUserActionsJournal(params)
  }, [params])

  useEffect(() => {
    setLoading(true)
    fetchExternalSystemStatuses()
  }, [])

  const [filteredUserActions, setFilteredUserActions] = useState<IUserActionsJournalResponse[]>([])

  useEffect(() => {
    setFilteredUserActions(userActionsJournalList)
    setTimeout(() => setLoading(false), TIME_LOADER * 2)
  }, [userActionsJournalList])

  useEffect(() => {
    if (!userActionsCategoryList.length) return
    const filters: { name: string; label: string; checked: boolean }[] = []
    userActionsCategoryList?.forEach((category: IUserActionsCategory) =>
      filters.push({
        name: category.code,
        label: category.name,
        checked: false,
      }),
    )
    setFilter({ categories: filters })
  }, [userActionsCategoryList])

  const handleChangeDate = (value: DataPickerValue, context: DatePickerContext) => {
    if (context.validationError.some((item) => item)) return
    const [fromDate, toDate] = value
    setParams((prev) => ({
      ...prev,
      fromDate,
      toDate,
    }))
  }

  const handleChangeFilter = (key: 'categories', values: string[]) => {
    const newFilter = [...filter[key]].map((item) => ({
      ...item,
      checked: values.includes(item.name),
    }))
    setFilter((prev) => ({ ...prev, [key]: newFilter }))
    setParams((prev) => {
      return values.length > 0 ? { ...prev, [key]: values.join(',') } : { ...prev, categories: [] }
    })
  }

  const handleChangeFilters = (type: 'categories', event: React.ChangeEvent<HTMLInputElement>) => {
    const newFilter = filter[type]
    const index = newFilter.findIndex((item) => item.name === event.target.name)
    newFilter.splice(index, 1, {
      ...newFilter[index],
      checked: event.target.checked,
    })
    handleChangeFilter(
      type,
      newFilter.filter((item) => item.checked).map((item) => item.name),
    )
    setFilter((prev) => (newFilter.length ? { ...prev, [type]: newFilter } : { ...prev }))
  }

  const columns = [
    {
      name: 'createdDate',
      title: 'Время(МСК)',
      width: 150,
      render: (value: string) => format(new Date(value), 'dd.MM.yyy HH:mm:ss'),
    },
    { name: 'login', title: 'Учётная запись', width: 200 },
    { name: 'ip', title: 'IP', width: 150 },
    { name: 'category', title: 'Раздел', width: 150 },
    { name: 'departmentName', title: 'ДЦ', width: 100 },
    {
      name: 'description',
      title: 'Действие',
      width: 900,
      isBlockedSorting: true,
      render: (description: string) => (
        <div
          style={{
            overflow: 'hidden',
            textWrap: 'nowrap',
            textOverflow: 'ellipsis',
          }}
          title={description}
        >
          {description}
        </div>
      ),
    },
  ]

  const handleFilters = (frontFilters: ITableFilters[]) => {
    setFiltersFromFront(frontFilters)
  }

  const handleDownloadXml = async () => {
    const paramsFromTable = Object.assign(
      {},
      ...filtersFromFront.map(({ columnName, value }) => ({
        [columnName]: value,
      })),
    )
    try {
      const resp = await getUserActionsJournalXls({
        ...params,
        ...paramsFromTable,
      })
      saveFile(resp)
    } catch (err) {
      console.log(err)
    }
  }

  return (
    <div>
      <SubtitleWithActions
        title='Журнал действия пользователей'
        actions={[
          <div className={cls.DateContainer}>
            <DateRangePicker dateFrom={params?.fromDate} dateTo={params?.toDate} handleChangeDate={handleChangeDate} />
          </div>,
          <Tooltip title='Скачать'>
            <IconButton style={{ padding: 0 }} onClick={handleDownloadXml}>
              <Icon className={cls.ExcelIcon} name='excel' width={24} height={24} />
            </IconButton>
          </Tooltip>,
        ]}
        isActionsVisible
      />
      <div className={classNames(cls.Body, {}, [])}>
        <Table
          isJournals
          getFilters={handleFilters}
          columns={columns}
          rows={filteredUserActions}
          height={height}
          loading={loading}
          columnSearchDisabled={['category']}
          headerComponents={
            <div className={cls.TopFilters}>
              <h4 className={cls.TopFilterTitle}>Раздел</h4>
              <div className={cls.TopFilterLabels}>
                {filter.categories.map((item) => (
                  <FormControlLabel
                    key={item.name}
                    className={cls.TopFilterLabel}
                    control={
                      <Checkbox
                        className={cls.TopFiltersCheckbox}
                        checked={item.checked}
                        name={item.name}
                        onChange={(value) => handleChangeFilters('categories', value)}
                      />
                    }
                    label={item.label}
                  />
                ))}
              </div>
            </div>
          }
        />
      </div>
    </div>
  )
})
