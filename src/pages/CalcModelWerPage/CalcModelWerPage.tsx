import { observer } from 'mobx-react'
import { ListOfCascades } from 'pages/CalcModelWerPage/ui/ListOfCascades'
import { ListOfStations } from 'pages/CalcModelWerPage/ui/ListOfStations'
import { useNavigate } from 'react-router-dom'
import { locationParse } from 'shared/lib/locationParse'
import { Tabs } from 'widgets/Tabs'
import { ItemsProps } from 'widgets/Tabs/Tabs'

import cls from './CalcModelWerPage.module.scss'
import { RunningTimes } from './ui/RunningTimes'

const tabs: ItemsProps[] = [
  {
    key: 'listofstations',
    label: 'Перечень станций',
    icon: 'book',
    isView: true,
  },
  {
    key: 'listofcascades',
    label: 'Перечень каскадов',
    icon: 'book',
    isView: true,
  },
  {
    key: 'runningtimes',
    label: 'Времена добегания',
    icon: 'book',
    isView: true,
  },
]

const CalcModelWerPage = observer(() => {
  const history = useNavigate()
  const { path = 'listofstations' } = locationParse(location.search)

  return (
    <div className={cls.calcModelPage}>
      <div className={cls.header}>
        <Tabs
          items={tabs}
          selectedValue={path}
          onChange={(value) => {
            history(`?path=${value}`)
          }}
        />
      </div>
      <div className={cls.body}>
        {path === 'listofstations' && <ListOfStations />}
        {path === 'listofcascades' && <ListOfCascades />}
        {path === 'runningtimes' && <RunningTimes />}
      </div>
    </div>
  )
})

export default CalcModelWerPage
