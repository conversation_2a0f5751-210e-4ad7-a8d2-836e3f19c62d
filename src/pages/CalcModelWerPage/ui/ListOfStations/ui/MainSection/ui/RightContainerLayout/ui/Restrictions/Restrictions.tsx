import { I<PERSON><PERSON><PERSON><PERSON>, Tooltip } from '@mui/material'
import { endOfDay, format, isAfter, startOfDay } from 'date-fns'
import { IWerRestriction } from 'entities/api/calcModelPage.entities.ts'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { observer } from 'mobx-react'
import { AddAndEditRestrictionModal } from 'pages/CalcModelWerPage/ui/ListOfStations/ui/MainSection/ui/RightContainerLayout/ui/Restrictions/ui/AddAndEditRestrictionModal'
import { useEffect, useMemo, useRef, useState } from 'react'
import { classNames } from 'shared/lib/classNames/classNames.ts'
import { formatDate, readableISO8601DateMonth } from 'shared/lib/dateFormates'
import { sortRestrictionsDate } from 'shared/lib/utils/dateSorting'
import { AccessControl } from 'shared/ui/AccessControl'
import { Icon } from 'shared/ui/Icon'
import { IWerRestrictionPrePost } from 'stores/CalcModelWerStore/WerListOfStationsStore/WerRestrictionsStore/WerRestrictionsStore.types'
import { useStore } from 'stores/useStore.ts'
import { Table } from 'widgets/Table'

import { useNotifyDayChanged } from '../../hooks/useNotifyDayChanged'
import cls from './Restriction.module.scss'

export const Restrictions = observer(() => {
  const tableContainerRef = useRef<HTMLDivElement>(null)
  const [isAddModal, setIsAddModal] = useState<boolean>(false)
  const [editableRestriction, setEditableRestriction] = useState<IWerRestrictionPrePost | null>(null)
  const [height, setHeight] = useState<number | null>(null)
  const isDayChanged = useNotifyDayChanged()
  const {
    calcModelWerStore: {
      date,
      selectedPlant,
      listOfStationsStore: { restrictionsStore },
    },
  } = useStore()
  const { isLoading, restrictions, originalRestrictions, init, deleteRestriction, reset, resetChanges } =
    restrictionsStore

  const disableAddEditRestriction = useMemo(
    () => !isAfter(startOfDay(date), endOfDay(new Date())) || selectedPlant?.viewOnly,
    [date, isDayChanged, selectedPlant],
  )

  const changeHeightTable = () => {
    if (tableContainerRef && tableContainerRef.current) {
      const el = tableContainerRef.current.querySelector('div')
      if (el) {
        setHeight(el.getBoundingClientRect().height)
      }
    }
  }

  useEffect(() => {
    changeHeightTable()
    window.addEventListener('resize', changeHeightTable)

    return () => {
      reset()
      resetChanges()
      window.removeEventListener('resize', changeHeightTable)
    }
  }, [])

  useEffect(() => {
    if (selectedPlant?.plantId) {
      resetChanges()
      init(selectedPlant?.plantId, format(date, 'yyyy-MM-dd'))
    }
  }, [selectedPlant, date])

  const columns = [
    {
      name: 'parameter',
      title: 'Параметр',
      width: 135,
      editingEnabled: false,
      render: (value: IWerRestriction['parameter']) => value.title,
    },
    {
      name: 'category',
      title: 'Категория',
      width: 100,
      editingEnabled: false,
      render: (value: IWerRestriction['category']) => value.title,
    },
    {
      name: 'type',
      title: 'Тип ограничения',
      width: 130,
      editingEnabled: false,
      render: (value: IWerRestriction['type']) => value.title,
    },
    {
      name: 'beginDate',
      title: 'Дата начала',
      width: 120,
      editingEnabled: false,
      customSorting: sortRestrictionsDate,
      render: (_: IWerRestriction['beginDate'], row: IWerRestriction) => {
        if (row.category.code === 'TEMPORARY' && row.temporaryBeginDate) {
          return formatDate(row.temporaryBeginDate)
        } else if (row.beginDate) {
          return readableISO8601DateMonth(row.beginDate)
        }

        return ''
      },
    },
    {
      name: 'stopDate',
      title: 'Дата окончания',
      width: 120,
      editingEnabled: false,
      customSorting: sortRestrictionsDate,
      render: (_: IWerRestriction['stopDate'], row: IWerRestriction) => {
        if (row.category.code === 'TEMPORARY' && row.temporaryEndDate) {
          return formatDate(row.temporaryEndDate)
        } else if (row.stopDate) {
          return readableISO8601DateMonth(row.stopDate)
        }

        return ''
      },
    },
    {
      name: 'minValue',
      title: 'Мин.',
      width: 80,
      editingEnabled: false,
      render: (value: IWerRestriction['minValue']) => value || '',
    },
    {
      name: 'maxValue',
      title: 'Макс.',
      width: 80,
      editingEnabled: false,
      render: (value: IWerRestriction['maxValue']) => value || '',
    },
    {
      name: 'valueType',
      title: 'Тип значения',
      width: 150,
      editingEnabled: false,
      render: (value: IWerRestriction['valueType']) => (value ? value.title : ''),
    },
    {
      name: 'comment',
      title: 'Комментарий',
      width: 200,
      editingEnabled: false,
      render: (value: IWerRestriction['comment']) => value || '',
    },
    {
      name: 'actions',
      title: '',
      width: 80,
      editingEnabled: false,
      headRender: () => {
        return (
          <AccessControl rules={[ROLES.TECH_ADMIN_CM]}>
            <div className={cls.actionHeader}>
              <Tooltip title='Создать ограничение'>
                <span>
                  <IconButton
                    sx={{ color: 'var(--primary-color)', display: 'inline-flex!important' }}
                    onClick={() => {
                      setIsAddModal(true)
                    }}
                    disabled={disableAddEditRestriction}
                  >
                    <Icon className={cls.addIcon} name='plus' width={13} />
                  </IconButton>
                </span>
              </Tooltip>
            </div>
          </AccessControl>
        )
      },
      render: (_: never, value: IWerRestrictionPrePost) => {
        return (
          <AccessControl rules={[ROLES.TECH_ADMIN_CM]}>
            <div className={cls.actions}>
              <Tooltip title='Настроить ограничение'>
                <span>
                  <IconButton
                    onClick={() => {
                      setEditableRestriction(value)
                    }}
                    className={classNames(cls.iconBlueBtn)}
                    disabled={disableAddEditRestriction}
                  >
                    <Icon name='settings' width={13} height={13} />
                  </IconButton>
                </span>
              </Tooltip>

              <Tooltip title='Удалить ограничение'>
                <span>
                  <IconButton
                    onClick={() => {
                      deleteRestriction(value)
                    }}
                    className={cls.iconRedBtn}
                    disabled={disableAddEditRestriction}
                  >
                    <Icon name='trash' width={13} height={13} />
                  </IconButton>
                </span>
              </Tooltip>
            </div>
          </AccessControl>
        )
      },
    },
  ]

  const handleClose = () => {
    setIsAddModal(false)
    setEditableRestriction(null)
  }

  return (
    <>
      {selectedPlant ? (
        <div ref={tableContainerRef} className={cls.fullHeight}>
          <Table
            rows={restrictions}
            columns={columns}
            height={height ?? 500}
            initialData={originalRestrictions ?? []}
            editMode
            columnSearchDisabled={[
              'parameter',
              'category',
              'type',
              'beginDate',
              'stopDate',
              'minValue',
              'maxValue',
              'valueType',
              'comment',
              'actions',
            ]}
            loading={isLoading}
          />
        </div>
      ) : (
        <div className={cls.noData}>Выберите станцию</div>
      )}
      {isAddModal && <AddAndEditRestrictionModal onClose={handleClose} />}
      {editableRestriction && <AddAndEditRestrictionModal onClose={handleClose} defaultValue={editableRestriction} />}
    </>
  )
})
