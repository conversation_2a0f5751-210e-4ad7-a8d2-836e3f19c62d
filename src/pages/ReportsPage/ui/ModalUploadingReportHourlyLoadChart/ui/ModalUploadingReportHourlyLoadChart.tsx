import { addDays, format, isAfter, isBefore, parse } from 'date-fns'
import { getReportName } from 'features/ModalMailingReportHourlyLoadChart/lib/getReportName.ts'
import { ModalReportProtocol } from 'features/ModalReportProtocol'
import { GroupsInput } from 'pages/ReportsPage/ui/GroupsInput/GroupsInput'
import { useEffect, useMemo, useRef, useState } from 'react'
import api from 'shared/api/index'
import { IStageType, ValidateUnloadingParams } from 'shared/api/reportsManager/reportsManager'
import { classNames } from 'shared/lib/classNames/classNames'
import { IStageList } from 'shared/lib/prepareReportDataTable/prepareReportAvrchmDataTable.ts'
import { prepareReportHourlyDataTable } from 'shared/lib/prepareReportDataTable/prepareReportHourlyDataTable.ts'
import { But<PERSON> } from 'shared/ui/Button'
import { DateRangePicker } from 'shared/ui/DateRangePicker'
import { DataPickerValue, DatePickerContext } from 'shared/ui/DateRangePicker/ui/DateRangePicker'
import { Loader } from 'shared/ui/Loader'
import { LoadingButton } from 'shared/ui/LoadingButton'
import { Modal } from 'shared/ui/Modal'
import { ModalWarning } from 'shared/ui/ModalWarning/ModalWarning'
import { Select } from 'shared/ui/Select'
import { TextField } from 'shared/ui/TextField'
import { TextFieldWithPrompt } from 'shared/ui/TextFieldWithPrompt/TextFieldWithPrompt'
import { useStore } from 'stores/useStore'
import { Table, TableProps } from 'widgets/Table'

import { ItemValue } from '../../ModalCreateEditReport/config/types'
import { validation } from '../lib/validate'
import { INITIAL_VALUES } from '../model/const'
import { Props, UnloadingDirectory, Values } from '../model/types'
import cls from './ModalUploadingReportHourlyLoadChart.module.scss'

export interface IRowModalUploadingReportHourlyLoadChart {
  tabId: string
  date: string
  planingStage: string
  stages: IStageList[]
}

export const ModalUploadingReportHourlyLoadChart = (props: Props) => {
  const { reportType, id, onClose } = props
  const { reportsStore, notificationStore } = useStore()
  const {
    unloadingFormatList,
    unloadingTemplateList,
    namePlaceholders,
    fetchUnloadingFormatList,
    fetchUnloadingTemplateList,
    fetchNamePlaceholders,
    validateUnloading,
    validateData,
  } = reportsStore

  const [values, setValues] = useState<Values>(INITIAL_VALUES)
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [isLoadingValues, setIsLoadingValues] = useState(false)
  const [isVisibleModalProtocol, setIsVisibleModalProtocol] = useState(false)

  const fetchReport = async (id: number) => {
    try {
      const res = await api.reportsManager.getReport(id)

      setValues(
        (prev) =>
          ({
            ...prev,
            name: res.name,
            unloadingFormat: res?.settings?.unloadingFormat,
            unloadingTemplate: {
              oldValue: res?.settings?.unloadingTemplate,
              newValue: res?.settings?.unloadingTemplate,
            },
            plants: res?.settings?.plants,
            fileNameTemplate: res?.settings?.fileNameTemplate,
            unloadingDirectories:
              res?.settings?.unloadingDirectories?.map((item) => ({
                value: item,
                checked: true,
              })) || [],
          }) as Values,
      )
    } catch (error) {
      console.log(error)
    }
  }

  const fetchStages = async (dateFrom: Date, dateTo: Date) => {
    let date = dateFrom
    const calcDates = [format(date, 'yyyy-MM-dd')]

    while (isBefore(date, dateTo)) {
      date = addDays(date, 1)
      calcDates.push(format(date, 'yyyy-MM-dd'))
    }

    try {
      const res = await api.calculationsManager.getListStages({
        calcDates,
      })
      setRows(prepareReportHourlyDataTable(res.dateStages))
    } catch (error) {
      console.log(error)
    }
  }

  const fetchUnloadingDates = async (id: number) => {
    try {
      const res = await api.reportsManager.getUnloadingDates(id)

      const dates = Object.keys(res.dateStages)
      if (dates.length) {
        let minDate = new Date(Object.keys(res.dateStages)[0])
        let maxDate = new Date(Object.keys(res.dateStages)[0])

        for (const date of dates) {
          if (isBefore(new Date(date), minDate)) minDate = new Date(date)
          else if (isAfter(new Date(date), maxDate)) maxDate = new Date(date)
        }

        setValues((prev: Values) => ({
          ...prev,
          dateFrom: minDate,
          dateTo: maxDate,
        }))
      }

      const [firstDataItem] = Object.values(res.dateStages)
      if (firstDataItem) {
        setStagesList(
          firstDataItem.stages.map((item) => ({
            value: item.code,
            label: item.title,
          })),
        )
      }

      setRows(prepareReportHourlyDataTable(res.dateStages))
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    const getData = async () => {
      try {
        setIsLoadingValues(true)

        await Promise.all([fetchUnloadingFormatList(), fetchUnloadingTemplateList(), fetchNamePlaceholders()])
        await Promise.all([fetchReport(id as number), fetchUnloadingDates(id as number)])
      } catch (error) {
        console.log(error)
      } finally {
        setIsLoadingValues(false)
      }
    }

    void getData()

    return () => {
      reportsStore.resetValidateData()
    }
  }, [])

  const [validationInProgress, setValidationInProgress] = useState(false)

  const handleValidateUnload = async () => {
    const [isValid, errors] = validation(values)
    setErrors(errors)
    if (!isValid) return

    const calculationRequests = rows.map((item) => ({
      targetDate: format(parse(item?.date, 'dd.MM.yyyy', new Date()), 'yyyy-MM-dd'),
      planingStage: item?.planingStage,
    }))
    const params = {
      reportId: id,
      reportType,
      unloadingParameters: {
        fileNameTemplate: values.fileNameTemplate,
        calculationRequests,
        unloadingFormat: values.unloadingFormat,
        unloadingTemplate: values.unloadingTemplate.newValue,
        unloadingDirectories: values.unloadingDirectories
          .filter((item: UnloadingDirectory) => item.checked)
          .map((item: UnloadingDirectory) => item.value),
      },
    }

    try {
      setValidationInProgress(true)
      const data = await validateUnloading(params as unknown as ValidateUnloadingParams)

      if (data?.errors?.length || data?.warnings?.length) {
        setIsVisibleModalProtocol(true)
      } else if (!data?.errors?.length && !data?.warnings?.length) {
        handleUnload()
      }
    } catch (error) {
      console.log('error', error)
    } finally {
      setValidationInProgress(false)
    }
  }

  const unloadingReportAbortControllerRef = useRef<AbortController | null>(null)

  const [isUnloadingReport, setIsUnloadingReport] = useState(false)
  const [reportUnloadingName, setReportUnloadingName] = useState('')

  const handleUnload = async () => {
    unloadingReportAbortControllerRef.current?.abort()

    unloadingReportAbortControllerRef.current = new AbortController()

    const calculationRequests = rows.map((item) => ({
      targetDate: format(parse(item.date, 'dd.MM.yyyy', new Date()), 'yyyy-MM-dd'),
      planingStage: item.planingStage,
    }))
    const params = {
      reportId: id,
      reportType,
      unloadingParameters: {
        fileNameTemplate: values.fileNameTemplate,
        calculationRequests,
        unloadingFormat: values.unloadingFormat,
        unloadingTemplate: values.unloadingTemplate.newValue,
        unloadingDirectories: values.unloadingDirectories
          .filter((item: UnloadingDirectory) => item.checked)
          .map((item: UnloadingDirectory) => item.value),
      },
    }

    const reportName = getReportName(
      values.fileNameTemplate,
      values.dateFrom,
      values.dateTo,
      stagesList,
      rows[0].planingStage,
    )
    setReportUnloadingName(reportName)

    try {
      setIsUnloadingReport(true)
      setIsVisibleModalProtocol(false)

      const unloadingResponse = await api.reportsManager.unloadingReport(
        params as unknown as ValidateUnloadingParams,
        unloadingReportAbortControllerRef.current.signal,
      )

      if (unloadingResponse.errors.length) {
        notificationStore.addNotification({
          title: 'Ошибка',
          description: unloadingResponse.result,
          type: 'warning',
        })

        reportsStore.validateData = unloadingResponse
        setIsVisibleModalProtocol(true)
      } else {
        notificationStore.addNotification({
          title: '',
          description: unloadingResponse.result,
          type: 'success',
        })

        onClose()
      }
    } catch (error) {
      console.log(error)
    } finally {
      setIsUnloadingReport(false)
      setIsVisibleModalWarning(false)
    }
  }

  //
  // change values

  const handleChangevalue = (key: string, value: ItemValue) => {
    switch (key) {
      case 'unloadingTemplate':
        setValues(
          (prev: Values) =>
            ({
              ...prev,
              [key]: {
                ...prev[key],
                newValue: value,
              },
            }) as Values,
        )
        break
      case 'unloadingFormat':
        setValues(
          (prev: Values) =>
            ({
              ...prev,
              [key]: value,
              unloadingTemplate: {
                ...prev.unloadingTemplate,
                newValue: prev.unloadingTemplate.oldValue,
              },
            }) as Values,
        )
        break
      default:
        setValues((prev: Values) => ({ ...prev, [key]: value }))
        break
    }

    if (errors[key] !== undefined)
      setErrors((prev) => {
        const newObj = { ...prev }
        delete newObj[key]

        return newObj
      })

    reportsStore.resetValidateData()
  }

  const handleChangeDate = (value: DataPickerValue, context: DatePickerContext) => {
    if (context.validationError.some((item) => item)) return
    reportsStore.resetValidateData()
    const [from, to] = value
    setValues(
      (prev: Values) =>
        ({
          ...prev,
          dateFrom: from,
          dateTo: to,
        }) as Values,
    )
    fetchStages(from as Date, to as Date)
  }
  const refTableBlock = useRef<HTMLDivElement>(null)

  const [rows, setRows] = useState<IRowModalUploadingReportHourlyLoadChart[]>([])
  const [stagesList, setStagesList] = useState<IStageList[]>([])

  const columns = [
    {
      name: 'date',
      title: 'Дата',
      width: 200,
    },
    {
      name: 'planingStage',
      title: 'Этап планирования',
      width: 200,
      editingEnabled: true,
      editType: 'select',
      selectDataFromRow: 'stages',
      render: (value: IStageType, row: IRowModalUploadingReportHourlyLoadChart) => {
        const currentItem = row.stages.find((el) => el?.value === (value as unknown as string))

        return <>{currentItem?.label ?? ''}</>
      },
    },
  ]

  const heightTable = useMemo(() => {
    return refTableBlock?.current?.clientHeight ?? 125
  }, [refTableBlock?.current?.clientHeight])

  //table

  const [isVisibleModalWarning, setIsVisibleModalWarning] = useState(false)

  const handleClose = () => {
    if (isUnloadingReport) {
      setIsVisibleModalWarning(true)
    } else {
      onClose()
    }
  }

  const handleResetUnloading = () => {
    unloadingReportAbortControllerRef.current?.abort()

    setIsVisibleModalWarning(false)
    onClose()
  }

  return (
    <Modal
      open
      title='Выгрузка отчёта почасового графика нагрузки'
      maxWidth='md'
      onClose={handleClose}
      actions={
        <div className={cls.modalFooter}>
          <div className={cls.modalFooterRight}>
            {validateData.errors.length > 0 && (
              <Button
                variant='outlined'
                onClick={() => setIsVisibleModalProtocol(true)}
                disabled={isLoadingValues || isUnloadingReport}
                className={cls.btnProtocol}
              >
                Протокол выгрузки
              </Button>
            )}
            <Button variant='outlined' onClick={handleClose} disabled={isUnloadingReport}>
              Отменить
            </Button>

            <LoadingButton
              variant='contained'
              className={cls.downloadButton}
              onClick={handleValidateUnload}
              disabled={validateData.errors.length > 0 || isLoadingValues || !!Object.keys(errors).length}
              loading={validationInProgress || isUnloadingReport}
            >
              Выгрузить
            </LoadingButton>
          </div>
        </div>
      }
    >
      <>
        <div
          className={classNames(
            cls.wrapper,
            {
              [cls.wrapperLoading]: isLoadingValues || isUnloadingReport,
            },
            [],
          )}
        >
          {isLoadingValues || isUnloadingReport ? (
            <Loader />
          ) : (
            <>
              <div className={cls.row}>
                <div className={cls.labelRow}>Отчёт</div>
                <div className={cls.valueBlock}>
                  <TextField
                    style={{ width: '100%' }}
                    value={values?.name}
                    type='string'
                    disabled
                    onChange={(e) => handleChangevalue('name', e.target.value)}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Дата (план)</div>
                <div className={cls.valueBlockShort}>
                  <DateRangePicker
                    dateFrom={values.dateFrom}
                    dateTo={values.dateTo}
                    handleChangeDate={handleChangeDate}
                  />
                </div>
              </div>
              <div ref={refTableBlock} className={cls.rowTable}>
                <Table
                  columns={columns}
                  rows={rows}
                  setRows={setRows as TableProps['setRows']}
                  height={heightTable}
                  editMode
                  columnSearchDisabled={['date', 'planingStage']}
                />
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Формат выгрузки</div>
                <div className={cls.valueBlockShort}>
                  <Select
                    variant='outlined'
                    items={unloadingFormatList}
                    value={values.unloadingFormat as unknown as string}
                    onChange={(value) => handleChangevalue('unloadingFormat', value)}
                    className={cls.selectFontSize}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Шаблон выгрузки</div>
                <div className={cls.valueBlockShort}>
                  <Select
                    disabled={values.unloadingFormat === 'CSV'}
                    variant='outlined'
                    items={unloadingTemplateList}
                    value={values.unloadingTemplate?.newValue as unknown as string}
                    onChange={(value) => handleChangevalue('unloadingTemplate', value)}
                    className={cls.selectFontSize}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Станции</div>
                <div className={cls.valueBlock}>
                  <TextField
                    value={values?.plants?.map((item: { name: string }) => item?.name ?? '').join(', ')}
                    type='string'
                    multiline
                    className={cls.textFieldInput}
                    disabled
                    onChange={(e) => handleChangevalue('name', e.target.value)}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Шаблон названия файла</div>
                <div className={cls.valueBlock}>
                  <TextFieldWithPrompt
                    error={!!errors.fileNameTemplate?.length}
                    helperText={errors.fileNameTemplate ?? ''}
                    className={cls.textFieldPropmt}
                    value={values.fileNameTemplate}
                    items={namePlaceholders}
                    onChange={(value: string) => handleChangevalue('fileNameTemplate', value)}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Директории выгрузки</div>
                <div className={cls.valueBlock}>
                  <GroupsInput
                    error={!!errors.unloadingDirectories?.length}
                    helperText={errors.unloadingDirectories ?? ''}
                    values={values.unloadingDirectories}
                    maxItems={5}
                    deleteMode={false}
                    checkedMode
                    type='directory'
                    onChange={(arr: UnloadingDirectory[]) =>
                      handleChangevalue('unloadingDirectories', arr as unknown as ItemValue)
                    }
                  />
                </div>
              </div>
            </>
          )}
        </div>

        {isVisibleModalProtocol && (
          <ModalReportProtocol
            handleClose={() => setIsVisibleModalProtocol(false)}
            handleAccept={handleUnload}
            acceptText='Выгрузить'
            closeText='Отменить'
            title='Выгрузка отчёта'
          />
        )}

        {isVisibleModalWarning && (
          <ModalWarning
            description={`Выгрузка отчёта "${reportUnloadingName}" будет прервана`}
            handleReset={handleResetUnloading}
            handleClose={() => setIsVisibleModalWarning(false)}
          />
        )}
      </>
    </Modal>
  )
}
