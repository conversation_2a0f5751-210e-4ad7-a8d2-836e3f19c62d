// История действий
import { Checkbox, FormControlLabel } from '@mui/material'
import { format } from 'date-fns'
import { TIME_LOADER } from 'entities/constants.ts'
import { observer } from 'mobx-react'
import { useEffect, useState } from 'react'
import { type IRegistryEventsParams } from 'shared/api/nsiManager/nsiManager'
import { classNames } from 'shared/lib/classNames/classNames'
import { glossary } from 'shared/lib/glossary/glossary'
import { DateRangePicker } from 'shared/ui/DateRangePicker'
import { DataPickerValue, DatePickerContext } from 'shared/ui/DateRangePicker/ui/DateRangePicker'
import { Icon } from 'shared/ui/Icon'
import { Modal } from 'shared/ui/Modal'
import { useStore } from 'stores/useStore'
import { Table } from 'widgets/Table'
import { type IColumn } from 'widgets/Table/ui/Table'

import cls from './ModalChangeLog.module.scss'

export interface ModalChangeLogProps {
  className?: string
  onClose?: () => void
}

export interface IRow {
  registryType?: {
    code?: 'plant' | 'rge' | 'generator' | 'department'
  }
}

const getIconTypeObject = (type: string) => {
  if (type === 'PLANT') {
    return 'plant'
  }
  if (type === 'RGU') {
    return 'rge'
  }
  if (type === 'GENERATOR') {
    return 'generator'
  }

  return 'department'
}

const INITIAL_FILTER = {
  registryEventTypes: [
    {
      name: 'ADDED',
      label: (
        <div className={cls.RowFilter}>
          <Icon name='circlePlus' width={20} height={20} />
          <div>Добавленные</div>
        </div>
      ),
      checked: false,
    },
    {
      name: 'UPDATED',
      label: (
        <div className={cls.RowFilter}>
          <Icon name='circleInfo' width={20} height={20} />
          <div>Измененные</div>
        </div>
      ),
      checked: false,
    },
    {
      name: 'DELETED',
      label: (
        <div className={cls.RowFilter}>
          <Icon name='circleMinus' width={20} height={20} />
          <div>Удаленные</div>
        </div>
      ),
      checked: false,
    },
  ],
  registryTypes: [
    {
      name: 'DEPARTMENT',
      label: (
        <div className={classNames(cls.LabelFilter, {}, [])}>
          <Icon width={18} name={getIconTypeObject('DEPARTMENT')} className={cls.IconFilter} />
          <>{glossary.DEPARTMENT}</>
        </div>
      ),
      checked: false,
    },
    {
      name: 'PLANT',
      label: (
        <div className={classNames(cls.LabelFilter, {}, [])}>
          <Icon width={18} name={getIconTypeObject('PLANT')} className={`${cls.IconFilter} ${cls.IconFilterBlue}`} />
          <>{glossary.PLANT}</>
        </div>
      ),
      checked: false,
    },
    {
      name: 'RGU',
      label: (
        <div className={classNames(cls.LabelFilter, {}, [])}>
          <Icon width={18} name={getIconTypeObject('RGU')} className={`${cls.IconFilter} ${cls.IconFilterBlue}`} />
          <>{glossary.RGU}</>
        </div>
      ),
      checked: false,
    },
    {
      name: 'GENERATOR',
      label: (
        <div className={classNames(cls.LabelFilter, {}, [])}>
          <Icon
            width={18}
            name={getIconTypeObject('GENERATOR')}
            className={`${cls.IconFilter} ${cls.IconFilterBlue}`}
          />
          <>{glossary.GENERATOR}</>
        </div>
      ),
      checked: false,
    },
  ],
}

const getIcon = (type: string) => {
  switch (type) {
    case 'ADDED':
      return <Icon name='circlePlus' width={20} height={20} />
    case 'DELETED':
      return <Icon name='circleMinus' width={20} height={20} />
    case 'UPDATED':
      return <Icon name='circleInfo' width={20} height={20} />
    default:
      return <Icon name='circleInfo' width={20} height={20} />
  }
}

export const ModalChangeLog = observer((props: ModalChangeLogProps) => {
  const { className, onClose } = props
  const { nsiStore } = useStore()
  const { registryEvents, getRegistryEvents } = nsiStore

  const [filter, setFilter] = useState(INITIAL_FILTER)

  const columns: IColumn[] = [
    {
      name: 'eventType',
      title: '',
      width: 55,
      isHeadLabelCenter: true,
      render: (value: string) => <div className={cls.IconCell}>{getIcon(value)}</div>,
    },
    { name: 'departmentName', title: 'ДЦ', width: 200 },
    { name: 'plantName', title: 'Станция', width: 180 },
    {
      name: 'name',
      title: 'Объект',
      width: 190,
      render: (value: string, row: IRow) => {
        const code = row?.registryType?.code ?? null
        const icon = code ? getIconTypeObject(code) : null

        return (
          <>
            {icon && (
              <div
                className={classNames(
                  cls.IconTypeRow,
                  {
                    [cls.BlueIcon]: icon === 'rge' || icon === 'generator' || icon === 'plant',
                  },
                  [],
                )}
              >
                <Icon width={18} name={icon} />
              </div>
            )}
            <>{value}</>
          </>
        )
      },
    },
    { name: 'parameter', title: 'Параметр', width: 180 },
    { name: 'oldValue', title: 'Было', width: 180 },
    { name: 'newValue', title: 'Стало', width: 180 },
    {
      name: 'eventDate',
      title: 'Время изменения',
      width: 180,
      defaultSorting: true,
      render: (value: string) => format(new Date(value), 'dd.MM.yyy HH:mm:ss'),
    },
    {
      name: 'actor',
      title: 'Инициатор',
      width: 150,
      render: (value: string) => {
        return <>{value.trim().toUpperCase() === 'SYSTEM' ? <Icon name='ghost' width={18} /> : <>{value}</>}</>
      },
    },
    { name: 'uid', title: 'UID', width: 300 },
  ]

  const [params, setParams] = useState<IRegistryEventsParams>({
    fromDate: new Date(),
    toDate: new Date(),
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    setLoading(true)
    getRegistryEvents(params).then(() => {
      setTimeout(() => {
        setLoading(false)
      }, TIME_LOADER)
    })
  }, [params])

  const handleChangeFilter = (key: 'registryEventTypes' | 'registryTypes', values: string[]) => {
    const newFilter = [...filter[key]].map((item) => ({
      ...item,
      checked: values.includes(item.name),
    }))
    setFilter((prev) => ({
      ...prev,
      [key]: newFilter,
    }))
    setParams((prev) => ({
      ...prev,
      [key]: values,
    }))
  }

  const handleChangeFilters = (
    type: 'registryTypes' | 'registryEventTypes',
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const newFilter = filter[type]
    const index = newFilter.findIndex((item) => item.name === event.target.name)
    newFilter.splice(index, 1, {
      ...newFilter[index],
      checked: event.target.checked,
    })
    handleChangeFilter(
      type,
      newFilter.filter((item) => item.checked).map((item) => item.name),
    )
    setFilter((prev) => ({ ...prev, [type]: newFilter }))
  }

  const handleChangeDate = (value: DataPickerValue, context: DatePickerContext) => {
    if (context.validationError.some((item) => item)) return
    const [from, to] = value
    setParams((prev) => ({
      ...prev,
      fromDate: from,
      toDate: to,
    }))
  }

  return (
    <Modal
      title='Журнал изменений'
      maxWidth={false}
      className={classNames(cls.ModalUploadStations, {}, className ? [className] : [])}
      onClose={onClose}
    >
      <div className={classNames(cls.DateContainer, {}, [])}>
        <DateRangePicker dateFrom={params.fromDate} dateTo={params.toDate} handleChangeDate={handleChangeDate} />
      </div>
      <div className={classNames(cls.TableContainer, {}, [])}>
        <Table
          rows={registryEvents}
          columns={columns}
          height={730}
          columnSearchDisabled={['eventType']}
          loading={loading}
          headerComponents={
            <>
              <div className={cls.TopFilters}>
                <h4 className={cls.TopFilterTitle}>Статус объекта</h4>
                {filter.registryEventTypes.map((item, index) => (
                  <FormControlLabel
                    key={item.name}
                    className={index === 3 ? cls.TopFiltersLongLabel : cls.TopFiltersLabel}
                    control={
                      <Checkbox
                        className={cls.TopFiltersCheckbox}
                        checked={item.checked}
                        name={item.name}
                        onChange={(e) => handleChangeFilters('registryEventTypes', e)}
                      />
                    }
                    label={item.label}
                  />
                ))}
              </div>
              <div className={cls.TopFilters}>
                <h4 className={cls.TopFilterTitle}>Тип объекта</h4>
                {filter.registryTypes.map((item) => (
                  <FormControlLabel
                    key={item.name}
                    className={cls.TopFiltersLabel}
                    control={
                      <Checkbox
                        className={cls.TopFiltersCheckbox}
                        checked={item.checked}
                        name={item.name}
                        onChange={(e) => handleChangeFilters('registryTypes', e)}
                      />
                    }
                    label={item.label}
                  />
                ))}
              </div>
            </>
          }
        />
      </div>
    </Modal>
  )
})
