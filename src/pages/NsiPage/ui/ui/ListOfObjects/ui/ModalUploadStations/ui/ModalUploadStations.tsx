import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import { Checkbox, FormControlLabel, Tooltip } from '@mui/material'
import { addMonths, format, set } from 'date-fns'
import { IPlantsSK11, IValidateSyncInput } from 'entities/api/nsiManager.entities.ts'
import { TIME_LOADER } from 'entities/constants.ts'
import { IRowDepartmentsVer } from 'entities/pages/nsiPage.entities.ts'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { observer } from 'mobx-react'
import { Fragment, ReactNode, useEffect, useRef, useState } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import api from 'shared/api'
import { classNames } from 'shared/lib/classNames/classNames.ts'
import { sortFormattedDates } from 'shared/lib/utils/dateSorting'
import { Button } from 'shared/ui/Button'
import { CheckEditComponent } from 'shared/ui/CheckEditComponent'
import { Icon } from 'shared/ui/Icon'
import { type IconNameProps } from 'shared/ui/Icon/Icon.type.ts'
import { Modal } from 'shared/ui/Modal'
import { useStore } from 'stores/useStore.ts'
import { Table } from 'widgets/Table'

import cls from './ModalUploadStations.module.scss'

export type TIcons = 'ADDED' | 'EARLY_ADDED' | 'CHANGED' | 'UNCHANGED' | 'DELETED'

export type TUpdatedFields = {
  parameter: string
  oldValue: string
  newValue: string
}

export interface IRow {
  updatedFields: TUpdatedFields[]
  type: 'ADDED' | 'EARLY_ADDED' | 'CHANGED' | 'UNCHANGED' | 'DELETED'
  tabId: string
  uid: string | null
  startDate?: string
  endDate?: string
  marketCalcModelId?: number | null
  planDepartmentIds: null | string[]
  werDepartments: null | { value: number }[]
  planDepartments: { value: string | null }[] //null | string[] | null[] |
  cellsErrors: string[]
  tempEdits: string[]
}

const prepareDate = (date: string) => {
  const cur = new Date(date)
  const day = cur.getDate() > 9 ? cur.getDate() : `0${cur.getDate()}`
  const month = cur.getMonth() + 1 > 9 ? cur.getMonth() + 1 : `0${cur.getMonth() + 1}`
  const hours = cur.getHours() > 9 ? cur.getHours() : `0${cur.getHours()}`
  const min = cur.getMinutes() > 9 ? cur.getMinutes() : `0${cur.getMinutes()}`
  const sec = cur.getSeconds() > 9 ? cur.getSeconds() : `0${cur.getSeconds()}`

  return `${day}.${month}.${cur.getFullYear()} ${hours}:${min}:${sec}`
}

const TooltipLocal = (props: { updatedFields: TUpdatedFields[]; children: ReactNode }) => {
  const { updatedFields, children } = props
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div
      onMouseEnter={() => {
        setIsOpen(true)
      }}
      onMouseLeave={() => {
        setIsOpen(false)
      }}
      className={classNames(cls.TooltipChildren, {}, [])}
    >
      <>{children}</>
      {isOpen && (
        <div className={classNames(cls.TooltipContent, {}, [])}>
          {updatedFields.map((el, index) => {
            return (
              <Fragment key={`${el.parameter}-${index.toString()}`}>
                <div className={classNames(cls.TooltipRow, {}, [])}>
                  <div className={classNames(cls.TooltipCell, {}, [])}>Параметр:</div>
                  <div className={classNames(cls.TooltipCell, {}, [])}>{el.parameter}</div>
                </div>
                <div className={classNames(cls.TooltipRow, {}, [])}>
                  <div className={classNames(cls.TooltipCell, {}, [])}>Было:</div>
                  <div className={classNames(cls.TooltipCell, {}, [])}>{el.oldValue}</div>
                </div>
                <div className={classNames(cls.TooltipRow, {}, [])}>
                  <div className={classNames(cls.TooltipCell, {}, [])}>Стало:</div>
                  <div className={classNames(cls.TooltipCell, {}, [])}>{el.newValue}</div>
                </div>
                <div className={classNames(cls.TooltipRow, {}, [cls.LineContainer])}>
                  <div className={classNames(cls.TooltipLine, {}, [])}></div>
                </div>
              </Fragment>
            )
          })}
        </div>
      )}
    </div>
  )
}

interface ModalUploadStationsProps {
  hierarchyType?: 'BY_GENERATOR' | 'BY_RGU'
  className?: string
  modelVersion?: string
  loadDate?: string
  onClose?: () => void
  departments?: IRowDepartmentsVer[]
}

const prepareSaveDate = (date: string) => {
  return date.split('.').reverse().join('-')
}

const TYPES_FILTER = [
  {
    name: 'DELETED',
    label: (
      <div className={classNames(cls[`DELETED-icon`], {}, [cls.IconCombobox])}>
        <Icon width={20} className={cls.TableFilterIcon} name='calendarDelete' />
        <div className={classNames(cls.TextCombobox, {}, [])}>Удалённая</div>
      </div>
    ),
    checked: false,
  },
  {
    name: 'CHANGED',
    label: (
      <div className={classNames(cls[`CHANGED-icon`], {}, [cls.IconCombobox])}>
        <Icon className={cls.TableFilterIcon} width={20} name='calendarEdit' />
        <div className={classNames(cls.TextCombobox, {}, [])}>Изменённая</div>
      </div>
    ),
    checked: false,
  },
  {
    name: 'ADDED',
    label: (
      <div className={classNames(cls[`ADDED-icon`], {}, [cls.IconCombobox])}>
        <Icon className={cls.TableFilterIcon} width={20} name='calendarAdd' />
        <div className={classNames(cls.TextCombobox, {}, [])}>Новая</div>
      </div>
    ),
    checked: false,
  },
  {
    name: 'UNCHANGED',
    label: (
      <div className={classNames(cls[`UNCHANGED-icon`], {}, [cls.IconCombobox])}>
        <Icon className={cls.TableFilterIcon} width={20} name='calendarDone' />
        <div className={classNames(cls.TextCombobox, {}, [])}>Неизменная</div>
      </div>
    ),
    checked: false,
  },
  {
    name: 'EARLY_ADDED',
    label: (
      <div className={classNames(cls[`EARLY_ADDED-icon`], {}, [cls.IconCombobox])}>
        <Icon className={cls.TableFilterIcon} width={20} name='calendarAdd' />
        <div className={classNames(cls.TextCombobox, {}, [])}>Ранее добавленная</div>
      </div>
    ),
    checked: false,
  },
]

export const ModalUploadStations = observer((props: ModalUploadStationsProps) => {
  const { onClose, modelVersion, loadDate, hierarchyType, departments } = props
  const { nsiStore, authStore } = useStore()
  const { userDetail } = authStore
  const { initUploadStations, resetUploadStation, loadingStationSK11, plantsDiff, infoUploadStations, saveStations } =
    nsiStore
  const [selection, setSelection] = useState<string[]>([])
  const [rows, setRows] = useState<IRow[]>([])
  const [initialData, setInitialData] = useState<IRow[]>([])

  const init = {
    controller: null,
    init: () => initUploadStations(),
  }
  const abortControllerSyncPlantsRef = useRef<AbortController>(new AbortController())
  const abortControllerSyncPlants = abortControllerSyncPlantsRef.current
  const abortControllerSavePlantsRef = useRef<AbortController>(new AbortController())
  const abortControllerSavePlants = abortControllerSavePlantsRef.current
  useEffect(() => {
    init.init()

    return () => {
      resetUploadStation()
      abortControllerSyncPlants.abort()
      abortControllerSavePlants.abort()
    }
  }, [])

  const getIconsType = (type: TIcons): IconNameProps => {
    switch (type) {
      case 'ADDED':
        return 'calendarAdd'
      case 'EARLY_ADDED':
        return 'calendarAdd'
      case 'CHANGED':
        return 'calendarEdit'
      case 'UNCHANGED':
        return 'calendarDone'
      case 'DELETED':
        return 'calendarDelete'
      default:
        return 'calendarAdd'
    }
  }

  const [filter, setFilter] = useState(TYPES_FILTER)

  const handleChangeFilter = (values: string[]) => {
    const newFilter = [...filter].map((item) => ({
      ...item,
      checked: values.includes(item.name),
    }))
    setFilter(newFilter)
  }

  const handleChangeFilters = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newFilter = [...filter]
    const index = newFilter.findIndex((item) => item.name === event.target.name)
    newFilter.splice(index, 1, {
      ...newFilter[index],
      checked: event.target.checked,
    })
    handleChangeFilter(newFilter.filter((item) => item.checked).map((item) => item.name))
    setFilter(newFilter)
  }

  const columns = [
    {
      name: 'type',
      title: '',
      width: 70,
      isOverFlowVisible: true,
      render: (value: TIcons, row: IRow) => {
        return (
          <div className={classNames(cls[`${String(value)}-icon`], { [cls.typeIcon]: true }, [])}>
            {value === 'CHANGED' ? (
              <TooltipLocal updatedFields={row.updatedFields}>
                <Icon width={20} name={getIconsType(value)} />
              </TooltipLocal>
            ) : (
              <Icon width={20} name={getIconsType(value)} />
            )}
          </div>
        )
      },
      isHeadLabelCenter: true,
    },
    {
      name: 'name',
      title: 'Станция',
      width: 230,
      render: (value: string, row: IRow) => (
        <div className={classNames(cls.Station, {}, [])}>
          <div className={classNames(cls[row.type], {}, [])}>{value}</div>
        </div>
      ),
      editingEnabled: false,
    },
    {
      name: 'department',
      title: 'ДЦ',
      // fixed: "leftColumns",
      width: 200,
      render: (value: { name: string }) => <>{value?.name ?? ''}</>,
      editingEnabled: false,
      customSearching: (value: { name: string }, filter: { value: string }) => {
        return value?.name?.toUpperCase().includes(filter?.value?.toUpperCase())
      },
      customSorting: (a: { name: string }, b: { name: string }) => {
        const nameA = a?.name ?? ''
        const nameB = b?.name ?? ''

        return nameA.localeCompare(nameB)
      },
    },
    {
      name: 'werDepartments',
      title: 'ДЦ (ВЭР)',
      width: 182,
      editType: 'groups',
      editingEnabled: true,
      chooseItems: departments,
      chooseItemsOptions: { rule: 'nsi', isGroups: true },
      render: (value: { label: string }[]) => {
        return (
          <div className={classNames(cls.InputWrapper, {}, [])}>
            {value?.length > 0 &&
              value?.map((item, index) => {
                return (
                  <div className={classNames(cls.ChooseContainer, {}, [])} key={`autocomplete-tag-${index.toString()}`}>
                    {item?.label}
                  </div>
                )
              })}
          </div>
        )
      },
      customSearching: (value: { label: string }[], filter: { value: string }) => {
        const arr = value.map((el) => el?.label ?? '').join(' ')

        return arr.toUpperCase().includes(filter.value.toUpperCase())
      },
      canClearCell: true,
    },
    {
      name: 'planDepartments',
      title: 'ДЦ (план)',
      width: 182,
      editingEnabled: true,
      editType: 'groups',
      chooseItems: departments,
      chooseItemsOptions: { isGroups: true },
      render: (value: { name: string }[]) => {
        return (
          <div className={classNames(cls.InputWrapper, {}, [])}>
            {value?.length > 0 &&
              value?.map((item, index) => {
                return (
                  <div className={classNames(cls.ChooseContainer, {}, [])} key={`autocomplete-tag-${index.toString()}`}>
                    {item.name}
                  </div>
                )
              })}
          </div>
        )
      },
      customSearching: (value: { name: string }[], filter: { value: string }) => {
        const arr = value?.map((el) => el?.name ?? '').join(' ') ?? 'NULL'

        return arr.toUpperCase().includes(filter.value.toUpperCase())
      },
      onAfterChange: (_: unknown, row: IRow) => {
        setRows((prev) => {
          return prev.map((el) => {
            if (el.tabId === row.tabId) {
              return { ...el, disabledChecked: false }
            }

            return el
          })
        })
      },
    },
    {
      name: 'marketCalcModelId',
      title: 'ID Рыночной РМ',
      width: 240,
      editingEnabled: true,
      editType: 'text',
      onAfterChange: (_: unknown, row: IRow) => {
        setRows((prev) => {
          return prev.map((el) => {
            if (el.tabId === row.tabId) {
              return { ...el, disabledChecked: false }
            }

            return el
          })
        })
      },
    },
    {
      name: 'startDate',
      title: 'Дата начала',
      width: 170,
      editingEnabled: true,
      editType: 'date',
      customSorting: sortFormattedDates,
      onAfterChange: (value: string, row: IRow) => {
        setRows((prev) => {
          return prev.map((el) => {
            if (el.tabId === row.tabId) {
              const startDate =
                value === null || value === '0NaN.0NaN.NaN'
                  ? format(
                      set(addMonths(new Date(), 1), {
                        date: 1,
                      }),
                      'dd.MM.yyyy',
                    )
                  : value

              return {
                ...el,
                startDate,
                disabledChecked: false,
              }
            }

            return el
          })
        })
      },
    },
    {
      name: 'endDate',
      title: 'Дата окончания',
      width: 170,
      editingEnabled: true,
      editType: 'date',
      disablePast: true,
      customSorting: sortFormattedDates,
      onAfterChange: (_: unknown, row: IRow) => {
        setRows((prev) => {
          return prev.map((el) => {
            if (el.tabId === row.tabId) {
              return { ...el, disabledChecked: false }
            }

            return el
          })
        })
      },
    },
    {
      name: 'uid',
      title: 'UID',
      width: 300,
      editingEnabled: false,
      onAfterChange: (_: unknown, row: IRow) => {
        setRows((prev) => {
          return prev.map((el) => {
            if (el.tabId === row.tabId) {
              return { ...el, disabledChecked: false }
            }

            return el
          })
        })
      },
    },
  ]
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    setLoading(true)
    setRows(() => {
      const isFilter = filter.some((el) => el.checked)
      if (isFilter) {
        return plantsDiff
          .map((el) => {
            const disabledChecked = el.type === 'UNCHANGED'

            return { ...el, disabledChecked }
          })
          .filter((el) => {
            const res = filter ? filter.find((item) => item?.name === el?.type)?.checked : false

            return filter ? res : false
          }) as unknown as IRow[]
      }
      const res = plantsDiff.map((el) => {
        const disabledChecked = el.type === 'UNCHANGED'

        return { ...el, disabledChecked }
      })
      setInitialData(res as unknown as IRow[])

      return res as unknown as IRow[]
    })
    const timeoutId = setTimeout(() => {
      setLoading(false)
      clearTimeout(timeoutId)
    }, TIME_LOADER)
  }, [plantsDiff, filter])

  const onSave = async () => {
    const plants = selection.map((tabId: string) => {
      const find = rows.find((row) => row.tabId === tabId)
      const res = {
        type: find?.type ?? null,
        uid: find?.uid ?? null,
        startDate: find?.startDate && find?.startDate?.length > 0 ? prepareSaveDate(find?.startDate) : null,
        endDate: find?.endDate && find?.endDate?.length > 0 ? prepareSaveDate(find?.endDate) : null,
        planDepartmentIds:
          find?.planDepartments && find?.planDepartments?.length > 0
            ? find?.planDepartments.map((el) => el?.value)
            : null,
        marketCalcModelId: find?.marketCalcModelId ?? null,
        werDepartmentIds:
          find && find.werDepartments ? find.werDepartments.filter((el) => el).map((el) => el?.value) : [],
      }
      const finRes: Record<string, string | number | (string | null | number)[]> = {}
      for (const [key, value] of Object.entries(res)) {
        if (value !== null) {
          finRes[key] = value
        }
      }

      return finRes
    })
    const plantsRes = plants && plants.length > 0 ? plants : []
    try {
      const { errors } = await api.nsiManager.validateSync(
        {
          modelVersion: infoUploadStations?.modelVersion,
          plants: plantsRes as unknown as IValidateSyncInput['plants'],
        },
        abortControllerSyncPlantsRef.current.signal,
      )

      if (!errors.length) {
        await saveStations(plantsRes as unknown as IPlantsSK11[], abortControllerSavePlantsRef.current.signal).then(
          (isComplete: boolean) => {
            if (isComplete) {
              const accessRole = [ROLES.TECH_ADMIN_NSI]
              const editMode = userDetail.roles
                .map((el) => el.role)
                .some((el: string) => {
                  return accessRole.some((item) => item === el)
                })
              nsiStore.initListOfObjects(editMode, hierarchyType as unknown as 'BY_GENERATOR' | 'BY_RGU')

              onClose && onClose()
            }
          },
        )
      } else {
        const errorsMap = new Map()

        for (const error of errors) {
          const fieldName = error.field.toLocaleLowerCase().replace(/_([a-z])/g, (str: string) => str[1].toUpperCase())

          if (!errorsMap.has(error.uid)) {
            errorsMap.set(error.uid, {
              [fieldName]: error.message,
            })
          } else {
            errorsMap.set(error.uid, {
              ...errorsMap.get(error.uid),
              [fieldName]: error.message,
            })
          }
        }

        setRows((prev) =>
          prev.map((item) => ({
            ...item,
            cellsErrors: errorsMap.has(item.uid) ? Object.keys(errorsMap.get(item.uid)) : [],
          })),
        )
      }
    } catch (error) {
      console.log(error)
    }
  }
  const date = loadDate ? prepareDate(loadDate) : ''
  const handleRepeatInit = () => {
    init.init()
  }

  const handleReset = () => {
    setSelection([])
    setRows(() => {
      const isFilter = filter.some((el) => el.checked)
      if (isFilter) {
        return plantsDiff
          .map((el) => {
            const disabledChecked = el.type === 'UNCHANGED'

            return { ...el, disabledChecked }
          })
          .filter((el) => {
            return filter ? filter.find((item) => item?.name === el?.type)?.checked : false
          }) as unknown as IRow[]
      }

      return plantsDiff.map((el) => {
        const disabledChecked = el.type === 'UNCHANGED'

        return { ...el, disabledChecked }
      }) as unknown as IRow[]
    })
  }

  useHotkeys('ctrl+shift+s', () => selection.length > 0 && onSave())
  useHotkeys('ctrl+shift+x', () => selection.length > 0 && handleReset())

  const [height, setHeight] = useState<number | null>(null)

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      const dialogContent = document.getElementById('DialogContent')
      if (dialogContent?.clientHeight) {
        const computedStyles = getComputedStyle(dialogContent)
        const paddingTop = computedStyles.paddingTop ? parseFloat(computedStyles.paddingTop) : 0
        const paddingBottom = computedStyles.paddingBottom ? parseFloat(computedStyles.paddingBottom) : 0
        // вычисляем высоту контейнера с учетом paddings родителя
        setHeight(dialogContent.clientHeight - paddingTop - paddingBottom)
      }
    }, 0)

    return () => clearTimeout(timeoutId)
  }, [])

  const isEdit = rows.some((el) => el?.tempEdits && el?.tempEdits?.length > 0)

  return (
    <CheckEditComponent isEdit={isEdit}>
      <Modal
        className={cls.ModalUploadStations}
        open
        maxWidth={false}
        title='Загрузка станций'
        onClose={onClose}
        actions={
          <div className={classNames(cls.Actions, {}, [])}>
            {selection.length > 0 && (
              <div className={classNames(cls.ActionsLeft, {}, [])}>
                {selection.length > 0 && (
                  <>
                    <div className={classNames(cls.CheckboxIcon, {}, [])}>
                      <CheckCircleIcon />
                    </div>
                    <>Выбрано: {selection.length}</>
                  </>
                )}
              </div>
            )}
            {infoUploadStations.saveError && (
              <div className={cls.saveErrorDescription}>Ошибка сохранения станций: {infoUploadStations.saveError}</div>
            )}
            {selection.length > 0 && (
              <div className={classNames(cls.ActionsRight, {}, [])}>
                <Button disabled={loadingStationSK11 || loading} variant='outlined' onClick={handleReset}>
                  Сбросить
                </Button>
                <Button
                  disabled={rows.some((item) => item.cellsErrors && item.cellsErrors.length > 0)}
                  loading={loadingStationSK11 || loading}
                  variant='contained'
                  onClick={onSave}
                >
                  Сохранить
                </Button>
              </div>
            )}
          </div>
        }
      >
        {!infoUploadStations?.error ? (
          <Table
            isOverFlowVisible
            rows={rows}
            setRows={setRows}
            columns={columns}
            height={height}
            selectMode='many'
            selection={selection}
            setSelection={setSelection}
            editMode
            columnSearchDisabled={['type']}
            initialData={initialData}
            loading={loadingStationSK11 || loading}
            headerComponents={
              <>
                <div className={classNames(cls.HeaderTable, {}, [])}>
                  <div className={classNames(cls.HeaderCell, {}, [])}>
                    <Icon name='time' width={20} />
                    <div className={classNames(cls.ModeLabel, {}, [])}>
                      <Tooltip title='Время загрузки'>
                        <div>{date}</div>
                      </Tooltip>
                    </div>
                  </div>
                  <div className={classNames(cls.HeaderCell, {}, [])}>
                    <Icon name='groups' width={20} />
                    <div className={classNames(cls.ModeLabel, {}, [])}>
                      <Tooltip title='Версия'>
                        <div>{modelVersion ?? '0'}</div>
                      </Tooltip>
                    </div>
                  </div>
                </div>
                <div className={cls.TopFilters}>
                  <h4 className={cls.TopFilterTitle}>Статус станции</h4>
                  {filter.map((item) => (
                    <FormControlLabel
                      key={item.name}
                      className={cls.FilterLabel}
                      control={
                        <Checkbox
                          className={cls.TopFiltersCheckbox}
                          checked={item.checked}
                          name={item.name}
                          onChange={handleChangeFilters}
                        />
                      }
                      label={item.label}
                    />
                  ))}
                </div>
              </>
            }
          />
        ) : (
          <div className={cls.errorContainer}>
            <div className={cls.errorDescription}>Ошибка получения станций: {infoUploadStations.error}</div>
            <Button onClick={handleRepeatInit}>Повторить загрузку станций</Button>
          </div>
        )}
      </Modal>
    </CheckEditComponent>
  )
})
