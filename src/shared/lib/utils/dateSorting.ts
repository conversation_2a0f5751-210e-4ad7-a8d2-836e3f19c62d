/**
 * Универсальные функции для сортировки дат в таблицах
 */

/**
 * Сортировка дат в формате dd.MM.yyyy или dd.MM.yyyy HH:mm:ss
 * @param a - первая дата в формате dd.MM.yyyy или dd.MM.yyyy HH:mm:ss
 * @param b - вторая дата в формате dd.MM.yyyy или dd.MM.yyyy HH:mm:ss
 * @returns число для сортировки (-1, 0, 1)
 */
export const sortFormattedDates = (a: string, b: string): number => {
  if (!a && !b) return 0
  if (!a) return 1
  if (!b) return -1

  // Разбираем дату в формате dd.MM.yyyy или dd.MM.yyyy HH:mm:ss
  const parseFormattedDate = (dateStr: string): Date => {
    const [datePart, timePart] = dateStr.split(' ')
    const [day, month, year] = datePart.split('.')

    if (timePart) {
      const [hours, minutes, seconds] = timePart.split(':')
      return new Date(
        Number(year),
        Number(month) - 1, // месяц в Date начинается с 0
        Number(day),
        Number(hours),
        Number(minutes),
        Number(seconds),
      )
    } else {
      return new Date(
        Number(year),
        Number(month) - 1, // месяц в Date начинается с 0
        Number(day),
      )
    }
  }

  const dateA = parseFormattedDate(a)
  const dateB = parseFormattedDate(b)

  return dateA.getTime() - dateB.getTime()
}

/**
 * Сортировка дат в формате ISO (yyyy-MM-ddTHH:mm:ss или yyyy-MM-dd)
 * @param a - первая дата в формате ISO
 * @param b - вторая дата в формате ISO
 * @returns число для сортировки (-1, 0, 1)
 */
export const sortISODates = (a: string, b: string): number => {
  if (!a && !b) return 0
  if (!a) return 1
  if (!b) return -1

  const dateA = new Date(a)
  const dateB = new Date(b)

  return dateA.getTime() - dateB.getTime()
}

/**
 * Сортировка дат для компонента Restrictions (смешанные форматы)
 * @param a - первая дата (может быть в разных форматах)
 * @param b - вторая дата (может быть в разных форматах)
 * @param row - объект строки для получения дополнительной информации
 * @returns число для сортировки (-1, 0, 1)
 */
export const sortRestrictionsDate = (a: string, b: string, rowA: any, rowB: any): number => {
  if (!a && !b) return 0
  if (!a) return 1
  if (!b) return -1

  // Для временных ограничений используем temporaryBeginDate/temporaryEndDate
  const getDateValue = (value: string, row: any, dateField: 'beginDate' | 'stopDate') => {
    if (row?.category?.code === 'TEMPORARY') {
      const tempField = dateField === 'beginDate' ? 'temporaryBeginDate' : 'temporaryEndDate'
      return row[tempField] ? new Date(row[tempField]) : null
    }
    return value ? new Date(value) : null
  }

  const dateA = getDateValue(a, rowA, a === rowA?.beginDate ? 'beginDate' : 'stopDate')
  const dateB = getDateValue(b, rowB, b === rowB?.beginDate ? 'beginDate' : 'stopDate')

  if (!dateA && !dateB) return 0
  if (!dateA) return 1
  if (!dateB) return -1

  return dateA.getTime() - dateB.getTime()
}
