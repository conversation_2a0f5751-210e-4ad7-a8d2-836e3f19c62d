/**
 * Функция для сортировки дат в формате dd.MM.yyyy
 * @param a - первая дата в формате dd.MM.yyyy
 * @param b - вторая дата в формате dd.MM.yyyy
 * @returns число для сортировки (-1, 0, 1)
 */
export const sortFormattedDates = (a: string, b: string): number => {
  if (!a && !b) return 0
  if (!a) return 1
  if (!b) return -1
  
  // Конвертируем dd.MM.yyyy в Date для сравнения
  const parseDate = (dateStr: string) => {
    const [day, month, year] = dateStr.split('.')
    return new Date(Number(year), Number(month) - 1, Number(day))
  }
  
  const dateA = parseDate(a)
  const dateB = parseDate(b)
  
  return dateA.getTime() - dateB.getTime()
}
